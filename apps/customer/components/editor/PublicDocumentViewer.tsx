'use client'

import React, { useMemo } from 'react'
import { use<PERSON><PERSON><PERSON>, EditorContent } from '@tiptap/react'
import { StarterKit } from '@tiptap/starter-kit'
import { Markdown } from 'tiptap-markdown'
import { Image } from '@tiptap/extension-image'
import { TaskItem } from '@tiptap/extension-task-item'
import { TaskList } from '@tiptap/extension-task-list'
import { TextAlign } from '@tiptap/extension-text-align'
import { Typography } from '@tiptap/extension-typography'
import { Highlight } from '@tiptap/extension-highlight'
import { Subscript } from '@tiptap/extension-subscript'
import { Superscript } from '@tiptap/extension-superscript'
import { Underline } from '@tiptap/extension-underline'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableHeader } from '@tiptap/extension-table-header'
import { TableCell } from '@tiptap/extension-table-cell'
import { Link } from '@tiptap/extension-link'

// Custom Extensions (read-only versions)
import { ChartExtension } from '@/components/editor/extensions/chart-extension'
import { CitationExtension } from './extensions/CitationExtension'
import { ReportGroupExtension } from './extensions/ReportGroupComponent'
import { ReportSectionExtension } from '@/components/editor/extensions/ReportSectionExtension'
import { ReportSummaryExtension } from './extensions/ReportSummaryExtension'
import { ReferencesExtension } from './extensions/ReferencesExtension'
import { DetailsExtension } from './extensions/DetailsExtension'
import { MathematicsExtension } from './extensions/MathematicsExtension'

import { DocumentProvider } from './context/DocumentContext'
import { cn } from '@utils/lib/utils'

interface PublicDocumentViewerProps {
  title?: string
  content?: string
  data?: any
  citations?: any[]
  className?: string
}

export function PublicDocumentViewer({
  title,
  content,
  data,
  citations = [],
  className
}: PublicDocumentViewerProps) {
  // Create read-only extensions (no editing capabilities)
  const extensions = useMemo(() => [
    StarterKit.configure({
      // Disable history for read-only view
      history: false,
      // Disable dropcursor
      dropcursor: false,
    }),
    Markdown.configure({
      html: true,
      tightLists: true,
      linkify: false,
      breaks: false,
      transformPastedText: false,
      transformCopiedText: false,
    }),
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    Underline,
    TaskList,
    TaskItem.configure({
      nested: true,
    }),
    Highlight.configure({
      multicolor: true,
    }),
    Image,
    Typography,
    Superscript,
    Subscript,
    Table.configure({
      resizable: false, // Disable resizing in read-only mode
    }),
    TableRow,
    TableHeader,
    TableCell,
    Link.configure({
      openOnClick: true, // Allow clicking links in read-only mode
    }),

    // Custom Extensions (read-only)
    ChartExtension,
    CitationExtension.configure({
      citations: citations || [],
      admin: false, // Always false for public view
    }),
    ReportGroupExtension,
    ReportSectionExtension,
    ReportSummaryExtension,
    ReferencesExtension.configure({
      citations: citations || [],
    }),
    DetailsExtension,
    MathematicsExtension,
  ], [citations])

  // Create a read-only TipTap editor instance with print mode enabled
  const editor = useEditor({
    extensions,
    content: data || content || '',
    editable: false,
    enableInputRules: false,
    enablePasteRules: false,
  }, [data, content, extensions])

  return (
    <div className={cn('min-h-screen w-full bg-background', className)}>
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h1 className="text-2xl font-bold text-foreground">{title}</h1>
              )}
              <p className="text-sm text-muted-foreground mt-1">
                Shared document - Read only
              </p>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => window.print()}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
              >
                Print
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Document Content */}
      <main className="prose-container mx-auto px-4 py-8">
        <div
          className={cn(
            'prose prose-slate dark:prose-invert max-w-none',
            'prose-headings:scroll-mt-20',
            'prose-pre:bg-muted prose-pre:border',
            'prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:before:content-none prose-code:after:content-none',
            'prose-blockquote:border-l-primary prose-blockquote:bg-muted/50 prose-blockquote:px-4 prose-blockquote:py-2 prose-blockquote:rounded-r',
            'prose-table:border prose-th:border prose-td:border',
            'prose-img:rounded-lg prose-img:shadow-md',
            // Print styles
            'print:prose-sm print:max-w-none print:text-black',
            'print:prose-headings:text-black print:prose-p:text-black',
            'print:prose-strong:text-black print:prose-em:text-black',
            'print:prose-code:text-black print:prose-pre:text-black',
            'print:prose-blockquote:text-black print:prose-th:text-black print:prose-td:text-black',
            // Print mode class for public documents
            'eko-print-mode'
          )}
        >
          <DocumentProvider 
            documentId="public-document"
          >
            <EditorContent editor={editor} className="eko-print-mode"/>
          </DocumentProvider>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/50 mt-16 print:hidden">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center text-sm text-muted-foreground">
            <p>This document was shared publicly. Visit the original source for the latest version.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
