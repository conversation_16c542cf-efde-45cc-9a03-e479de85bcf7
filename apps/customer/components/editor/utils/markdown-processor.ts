import { unified } from 'unified'
import remarkParse from 'remark-parse'
import remarkGfm from 'remark-gfm'
import remarkRehype from 'remark-rehype'
import rehypeRaw from 'rehype-raw'
import rehypeStringify from 'rehype-stringify'
import { citationLink, CitationType } from '@/components/citation'
import { jsonrepair } from 'jsonrepair'
/* ------------------------------------------------------------------
 *  Shared citation regexes (same as eko-markdown.tsx)
 * ------------------------------------------------------------------*/
const singleCitationRegex = /\[\^(\d+)\]/g
const multipleCitationRegex = /\[\^(\d+(?:,\s*\^?\d+)*)\]/g

/* ------------------------------------------------------------------
 *  Types
 * ------------------------------------------------------------------*/
export interface ProcessedMarkdown {
  html: string
  footnotes: FootnoteData[]
}

export interface FootnoteData {
  id: number
  alt_id: number
  index: number
  citation: CitationType
  year: string | number
  text: string
  url: string
}

/* ------------------------------------------------------------------
 *  Citation processing function (adapted from eko-markdown.tsx)
 * ------------------------------------------------------------------*/
function processCitations(
  markdown: string,
  citations: CitationType[] | null,
  admin: boolean,
  inlineCitations: boolean = true,
  badgeStyle: boolean = true,
  citationPrefix: string = ''
): { content: string; footnotes: FootnoteData[] } {
  const footnotesArr: FootnoteData[] = []

  if (!citations || citations.length === 0) {
    return { content: markdown, footnotes: footnotesArr }
  }

  // Build citation lookup map - only use doc_page_id for citations
  const citationByPageId = new Map<number, CitationType>()
  citations.forEach(c => {
    if (c.doc_page_id) {
      citationByPageId.set(c.doc_page_id, c)
    }
  })

  let md = markdown

  // Extract all citation IDs
  const allIds: number[] = []

  // Grab single citations
  for (const m of md.matchAll(singleCitationRegex)) {
    allIds.push(+m[1])
  }

  // Grab multi-citations
  for (const m of md.matchAll(multipleCitationRegex)) {
    const parts = m[1].split(',').map(p => p.trim().replace('^', ''))
    parts.forEach(id => allIds.push(+id))
  }

  // Dedupe & sort
  const uniqueIds = Array.from(new Set(allIds)).sort((a, b) => a - b)

  // Build footnotes array
  uniqueIds.forEach((id, i) => {
    const c = citationByPageId.get(id)
    if (!c || !c.doc_page_id) return

    // Use sequential numbering for footnotes array (this is just for processing, actual numbering is handled by ReportStateManager)
    const citationNumber = i + 1

    footnotesArr.push({
      id: c.doc_page_id,
      alt_id: c.doc_id,
      index: citationNumber,
      citation: c,
      year: c.year || '',
      text: c.title,
      url: citationLink(c, admin),
    })
  })

  // Helper function to replace single citations
  const replaceSingle = (idStr: string) => {
    const id = +idStr
    const fn = footnotesArr.find(f => f.id === id || f.alt_id === id)
    if (!fn) return ''

    if (inlineCitations) {
      if (badgeStyle) {
        // Convert to new citation format for TipTap - use the doc_page_id from the footnote
        return `<citation page_id="${fn.id}"></citation>`
      }
      return `[(${fn.text.replace(/\d{4}$/, '')}, ${fn.year})](${fn.url})`
    }
    return ` [[${citationPrefix}${fn.index}]](${fn.url})`
  }

  // Replace multi-citations first
  md = md.replace(multipleCitationRegex, (_, list: string) => {
    if (!list.includes(',')) return _ // single – leave for next pass
    const ids = list
      .split(',')
      .map(p => p.trim().replace('^', ''))
      .sort((a, b) => +a - +b)
    return ids.map(replaceSingle).join('')
  })

  // Replace single citations
  md = md.replace(singleCitationRegex, (_, id: string) => replaceSingle(id))

  return { content: md, footnotes: footnotesArr }
}

/* ------------------------------------------------------------------
 *  Chart processing helper
 * ------------------------------------------------------------------*/
function preserveChartContent(markdown: string): string {
  // Find all chart tags and preserve their JSON content
  return markdown.replace(/<chart type="(\w+)">([\s\S]*?)<\/chart>/g, (match, chartType, jsonContent) => {
    try {
      // Clean up the JSON content - remove extra whitespace but preserve structure
      const cleanedJson = jsonrepair(jsonContent)

      // Validate that it's actually valid JSON before preserving
      JSON.parse(cleanedJson)

      // Encode the JSON content to prevent corruption during HTML processing
      const encodedJson = Buffer.from(cleanedJson).toString('base64')

      // Return the chart tag with encoded JSON content and a data attribute for the original
      return `<chart type="${chartType}" data-json="${encodedJson}">${cleanedJson}</chart>`
    } catch (error) {
      console.warn('Invalid JSON in chart tag, preserving as-is:', error)
      // If JSON is invalid, preserve the original content exactly as it was
      return match
    }
  }).replace(/<chart>([\s\S]*?)<\/chart>/g, (match, jsonContent) => {
    try {
      // Clean up the JSON content - remove extra whitespace but preserve structure  
      const cleanedJson = jsonrepair(jsonContent)

      // Validate that it's actually valid JSON before preserving
      JSON.parse(cleanedJson)

      // Encode the JSON content to prevent corruption during HTML processing
      const encodedJson = Buffer.from(cleanedJson).toString('base64')

      // Return the chart tag with encoded JSON content and a data attribute for the original
      return `<chart data-json="${encodedJson}">${cleanedJson}</chart>`
    } catch (error) {
      console.warn('Invalid JSON in chart tag, preserving as-is:', error)
      // If JSON is invalid, preserve the original content exactly as it was
      return match
    }
  })
}

/* ------------------------------------------------------------------
 *  Main markdown processor function
 * ------------------------------------------------------------------*/
export async function processMarkdownForTipTap(
  markdown: string,
  citations: CitationType[] | null = null,
  options: {
    admin?: boolean
    inlineCitations?: boolean
    badgeStyle?: boolean
    citationPrefix?: string
    skipCitations?: boolean
  } = {}
): Promise<ProcessedMarkdown> {
  // Validate input
  if (!markdown || typeof markdown !== 'string') {
    throw new Error('Invalid markdown input: must be a non-empty string')
  }

  const {
    admin = false,
    inlineCitations = true,
    badgeStyle = true,
    citationPrefix = '',
    skipCitations = false
  } = options

  let processedMarkdown = markdown
  let footnotes: FootnoteData[] = []

  try {
    // First, preserve chart content before any other processing
    processedMarkdown = preserveChartContent(processedMarkdown)

    // Process citations if not skipping them
    if (!skipCitations && citations) {
      const result = processCitations(
        processedMarkdown,
        citations,
        admin,
        inlineCitations,
        badgeStyle,
        citationPrefix
      )
      processedMarkdown = result.content
      footnotes = result.footnotes
    } else if (skipCitations) {
      // Remove all citations if skipping
      processedMarkdown = processedMarkdown.replaceAll(/\[\^.*]/g, '')
    }

    // Do NOT automatically add references section - this should be handled by the template
    // The references component will be added to document templates and will use ReportStateManager

    // Process markdown to HTML using unified/remark/rehype
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeStringify)

    const result = await processor.process(processedMarkdown)
    const html = String(result)

    // Validate output
    if (!html || typeof html !== 'string') {
      throw new Error('Markdown processing produced invalid HTML output')
    }

    return {
      html,
      footnotes
    }
  } catch (error) {
    console.error('Error in processMarkdownForTipTap:', error)
    // Re-throw with more context
    throw new Error(`Failed to process markdown: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/* ------------------------------------------------------------------
 *  Utility function to extract citations from markdown
 * ------------------------------------------------------------------*/
export function extractCitationIds(markdown: string): number[] {
  const allIds: number[] = []

  // Extract single citations
  for (const m of markdown.matchAll(singleCitationRegex)) {
    allIds.push(+m[1])
  }

  // Extract multi-citations
  for (const m of markdown.matchAll(multipleCitationRegex)) {
    const parts = m[1].split(',').map(p => p.trim().replace('^', ''))
    parts.forEach(id => allIds.push(+id))
  }

  return Array.from(new Set(allIds)).sort((a, b) => a - b)
}
