'use client'

import React, { useState } from 'react'
import { Editor } from '@tiptap/react'
import { Button } from '@ui/components/ui/button'
import { Separator } from '@ui/components/ui/separator'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Code,
  Highlighter,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Quote,
  Minus,
  Undo,
  Redo,
  Save,
  Type,
  Heading1,
  Heading2,
  Heading3,
  Table,
  Image,
  Link,
  Superscript,
  Subscript,
  Download,
  FileImage,
  FileSpreadsheet,
  FileText,
  BarChart3,
  FileBarChart,
  Layers,
  Printer,
  ListTree
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/ui/select'
import { ReportComponentDialog, ReportComponentConfig } from '../dialogs/ReportComponentDialog'
import { FeatureFlag } from '@/components/feature-flag'

export type ExportFormat = 'pdf' | 'docx' | 'html' | 'md'

interface EditorToolbarProps {
  editor: Editor
  isSaving?: boolean
  lastSaved?: Date | null
  onSave?: () => void
  onExport?: (format: ExportFormat) => void
  printMode?: boolean
  onTogglePrintMode?: () => void
}

export function EditorToolbar({
  editor,
  isSaving = false,
  lastSaved = null,
  onSave,
  onExport,
  printMode = false,
  onTogglePrintMode
}: EditorToolbarProps) {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogType, setDialogType] = useState<'report-section' | 'report-group' | 'report-summary'>('report-section')
  const handleHeadingChange = (value: string) => {
    if (value === 'paragraph') {
      editor.chain().focus().setParagraph().run()
    } else {
      const level = parseInt(value) as 1 | 2 | 3 | 4 | 5 | 6
      editor.chain().focus().toggleHeading({ level }).run()
    }
  }

  const addTable = () => {
    editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
  }

  const addImage = () => {
    const url = window.prompt('Enter image URL:')
    if (url) {
      editor.chain().focus().setImage({ src: url }).run()
    }
  }

  const addLink = () => {
    const url = window.prompt('Enter URL:')
    if (url) {
      // For now, just insert the URL as text
      // TODO: Add Link extension to support proper link functionality
      editor.chain().focus().insertContent(`[Link](${url})`).run()
    }
  }

  const addTableOfContents = () => {
    editor.chain().focus().setTableOfContents().run()
  }

  const openReportDialog = (type: 'report-section' | 'report-group' | 'report-summary') => {
    setDialogType(type)
    setDialogOpen(true)
  }

  const handleReportComponentConfirm = (config: ReportComponentConfig) => {
    console.log('handleReportComponentConfirm called with config:', config)

    try {
      // Use TipTap's insertContent with JSON format for better parsing
      let nodeContent: any

      if (config.type === 'report-section') {
        nodeContent = {
          type: 'reportSection',
          attrs: {
            id: config.id,
            title: config.title,
            endpoint: config.endpoint || '',
            prompt: config.prompt || null,
          },
          content: [
            {
              type: 'heading',
              attrs: { level: 2 },
              content: [
                {
                  type: 'text',
                  text: config.title || 'Report Section'
                }
              ]
            }
          ]
        }
      } else if (config.type === 'report-group') {
        nodeContent = {
          type: 'reportGroup',
          attrs: {
            id: config.id,
            title: config.title,
          },
          content: [
            {
              type: 'heading',
              attrs: { level: 2 },
              content: [
                {
                  type: 'text',
                  text: config.title || 'Report Group'
                }
              ]
            }
          ]
        }
      } else if (config.type === 'report-summary') {
        nodeContent = {
          type: 'reportSummary',
          attrs: {
            id: config.id,
            title: config.title,
            prompt: config.prompt || null,
            summarize: config.summarize?.join(',') || null,
          },
          content: [
            {
              type: 'heading',
              attrs: { level: 2 },
              content: [
                {
                  type: 'text',
                  text: config.title || 'Report Summary'
                }
              ]
            }
          ]
        }
      }

      console.log('Inserting node content:', nodeContent)

      // Insert the content using JSON format if nodeContent is defined
      if (nodeContent) {
        editor.chain().focus().insertContent(nodeContent).run()
      }

      console.log('Content insertion completed')

    } catch (error) {
      console.error('Error inserting component:', error)

      // Fallback to HTML insertion
      console.log('Trying HTML fallback...')
      let content = ''

      if (config.type === 'report-section') {
        content = `<report-section id="${config.id}" title="${config.title}" endpoint="${config.endpoint || ''}"${config.prompt ? ` prompt="${config.prompt}"` : ''}><h2>${config.title || 'Report Section'}</h2></report-section>`
      } else if (config.type === 'report-group') {
        content = `<report-group id="${config.id}"${config.title ? ` title="${config.title}"` : ''}><h2>${config.title || 'Report Group'}</h2></report-group>`
      } else if (config.type === 'report-summary') {
        const summarizeAttr = config.summarize?.join(',') || ''
        content = `<report-summary id="${config.id}" title="${config.title}" summarize="${summarizeAttr}"${config.prompt ? ` prompt="${config.prompt}"` : ''}><h2>${config.title || 'Report Summary'}</h2></report-summary>`
      }

      editor.chain().focus().insertContent(content).run()
    }
  }

  const getCurrentHeading = () => {
    if (editor.isActive('heading', { level: 1 })) return '1'
    if (editor.isActive('heading', { level: 2 })) return '2'
    if (editor.isActive('heading', { level: 3 })) return '3'
    if (editor.isActive('heading', { level: 4 })) return '4'
    if (editor.isActive('heading', { level: 5 })) return '5'
    if (editor.isActive('heading', { level: 6 })) return '6'
    return 'paragraph'
  }

  // Helper functions to safely check undo/redo availability
  const canUndo = () => {
    try {
      // First try the standard can() method
      if (editor.can && typeof editor.can === 'function') {
        return editor.can().undo()
      }
      // Fallback: check if undo command exists
      return editor.commands && typeof editor.commands.undo === 'function'
    } catch {
      // Final fallback: assume undo is available if commands exist
      return editor.commands && typeof editor.commands.undo === 'function'
    }
  }

  const canRedo = () => {
    try {
      // First try the standard can() method
      if (editor.can && typeof editor.can === 'function') {
        return editor.can().redo()
      }
      // Fallback: check if redo command exists
      return editor.commands && typeof editor.commands.redo === 'function'
    } catch {
      // Final fallback: assume redo is available if commands exist
      return editor.commands && typeof editor.commands.redo === 'function'
    }
  }

  return (
    <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60" data-testid="editor-toolbar">
      <div className="flex items-center gap-1 p-2 flex-wrap">
        {/* Undo/Redo */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            try {
              editor.chain().focus().undo().run()
            } catch (error) {
              console.warn('Undo failed:', error)
            }
          }}
          disabled={!canUndo()}
          title="Undo"
          data-testid="undo-button"
        >
          <Undo className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            try {
              editor.chain().focus().redo().run()
            } catch (error) {
              console.warn('Redo failed:', error)
            }
          }}
          disabled={!canRedo()}
          title="Redo"
          data-testid="redo-button"
        >
          <Redo className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Heading Selector */}
        <Select value={getCurrentHeading()} onValueChange={handleHeadingChange}>
          <SelectTrigger className="w-32 h-8" data-testid="heading-selector">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="paragraph">Paragraph</SelectItem>
            <SelectItem value="1">Heading 1</SelectItem>
            <SelectItem value="2">Heading 2</SelectItem>
            <SelectItem value="3">Heading 3</SelectItem>
            <SelectItem value="4">Heading 4</SelectItem>
            <SelectItem value="5">Heading 5</SelectItem>
            <SelectItem value="6">Heading 6</SelectItem>
          </SelectContent>
        </Select>

        <Separator orientation="vertical" className="h-6" />

        {/* Text Formatting */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'bg-muted' : ''}
          title="Bold"
          data-testid="bold-button"
        >
          <Bold className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'bg-muted' : ''}
          title="Italic"
          data-testid="italic-button"
        >
          <Italic className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={editor.isActive('underline') ? 'bg-muted' : ''}
          title="Underline"
          data-testid="underline-button"
        >
          <Underline className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'bg-muted' : ''}
          title="Strikethrough"
          data-testid="strikethrough-button"
        >
          <Strikethrough className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCode().run()}
          className={editor.isActive('code') ? 'bg-muted' : ''}
          title="Inline Code"
          data-testid="code-button"
        >
          <Code className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleHighlight().run()}
          className={editor.isActive('highlight') ? 'bg-muted' : ''}
          title="Highlight"
          data-testid="highlight-button"
        >
          <Highlighter className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Superscript/Subscript */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleSuperscript().run()}
          className={editor.isActive('superscript') ? 'bg-muted' : ''}
          title="Superscript"
        >
          <Superscript className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleSubscript().run()}
          className={editor.isActive('subscript') ? 'bg-muted' : ''}
          title="Subscript"
        >
          <Subscript className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Text Alignment */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('left').run()}
          className={editor.isActive({ textAlign: 'left' }) ? 'bg-muted' : ''}
          title="Align Left"
          data-testid="align-left-button"
        >
          <AlignLeft className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('center').run()}
          className={editor.isActive({ textAlign: 'center' }) ? 'bg-muted' : ''}
          title="Align Center"
          data-testid="align-center-button"
        >
          <AlignCenter className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('right').run()}
          className={editor.isActive({ textAlign: 'right' }) ? 'bg-muted' : ''}
          title="Align Right"
          data-testid="align-right-button"
        >
          <AlignRight className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('justify').run()}
          className={editor.isActive({ textAlign: 'justify' }) ? 'bg-muted' : ''}
          title="Justify"
          data-testid="justify-button"
        >
          <AlignJustify className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Lists */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'bg-muted' : ''}
          title="Bullet List"
          data-testid="bullet-list-button"
        >
          <List className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'bg-muted' : ''}
          title="Numbered List"
          data-testid="numbered-list-button"
        >
          <ListOrdered className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Block Elements */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'bg-muted' : ''}
          title="Quote"
        >
          <Quote className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setHorizontalRule().run()}
          title="Horizontal Rule"
        >
          <Minus className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Insert Elements */}
        <Button
          variant="ghost"
          size="sm"
          onClick={addTable}
          title="Insert Table"
          data-testid="insert-table-button"
        >
          <Table className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={addImage}
          title="Insert Image"
          data-testid="insert-image-button"
        >
          <Image className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={addLink}
          title="Insert Link"
          data-testid="insert-link-button"
        >
          <Link className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={addTableOfContents}
          title="Insert Table of Contents"
          data-testid="insert-toc-button"
        >
          <ListTree className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Report Components */}
        <FeatureFlag flag="document.editor.dynamic.reports">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openReportDialog('report-section')}
            title="Insert Report Section"
          >
            <BarChart3 className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openReportDialog('report-group')}
            title="Insert Report Group"
          >
            <Layers className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openReportDialog('report-summary')}
            title="Insert Report Summary"
          >
            <FileBarChart className="w-4 h-4" />
          </Button>
        </FeatureFlag>

        <Separator orientation="vertical" className="h-6" />

        {/* Save */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onSave}
          disabled={isSaving}
          title="Save Document"
          data-testid="save-button"
        >
          <Save className="w-4 h-4 mr-1" />
          {isSaving ? 'Saving...' : 'Save'}
        </Button>

        {/* Print Mode Toggle */}
        {onTogglePrintMode && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onTogglePrintMode}
            className={printMode ? 'bg-muted' : ''}
            title={printMode ? 'Exit Print Mode' : 'Enter Print Mode'}
            data-testid="print-toggle-button"
          >
            <Printer className="w-4 h-4 mr-1" />
            {printMode ? 'Normal' : 'Print'}
          </Button>
        )}

        {/* Export */}
        {onExport && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                title="Export Document"
                data-testid="export-button"
              >
                <Download className="w-4 h-4 mr-1" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onExport('pdf')}>
                <FileImage className="w-4 h-4 mr-2" />
                Export as PDF
              </DropdownMenuItem>
              <FeatureFlag flag="document.editor.export.word">
                <DropdownMenuItem onClick={() => onExport('docx')}>
                  <FileSpreadsheet className="w-4 h-4 mr-2" />
                  Export as Word
                </DropdownMenuItem>
              </FeatureFlag>
              <FeatureFlag flag="document.editor.export.markdown">
                <DropdownMenuItem onClick={() => onExport('md')}>
                  <FileText className="w-4 h-4 mr-2" />
                  Export as Markdown
                </DropdownMenuItem>
              </FeatureFlag>
              <FeatureFlag flag="document.editor.export.html">
                <DropdownMenuItem onClick={() => onExport('html')}>
                  <FileText className="w-4 h-4 mr-2" />
                  Export as HTML
                </DropdownMenuItem>
              </FeatureFlag>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {lastSaved && (
          <span className="text-xs text-muted-foreground ml-2">
            Last saved: {lastSaved.toLocaleTimeString()}
          </span>
        )}
      </div>

      <ReportComponentDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onConfirm={handleReportComponentConfirm}
        type={dialogType}
      />
    </div>
  )
}
