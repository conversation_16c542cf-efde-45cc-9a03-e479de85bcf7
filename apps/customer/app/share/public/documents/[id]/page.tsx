import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { PublicDocumentViewer } from '@/components/editor/PublicDocumentViewer'
import { createClient } from '@/app/supabase/server'

interface PublicDocumentPageProps {
  params: Promise<{
    id: string
  }>
}

// This function fetches the document data using Supabase client
async function getPublicDocument(id: string) {
  try {
    const supabase = await createClient()

    // Query the document with public access check
    // The RLS policy will handle access control for public documents
    const { data: document, error } = await supabase
      .from('collaborative_documents')
      .select('id, title, content, initial_content, data, metadata, created_at, updated_at, is_public')
      .eq('id', id)
      .eq('is_public', true) // Only fetch if document is public
      .single()

    if (error) {
      console.error('Error fetching public document:', error)
      return null
    }

    return document
  } catch (error) {
    console.error('Error fetching public document:', error)
    return null
  }
}

// Generate metadata for the page
export async function generateMetadata({ params }: PublicDocumentPageProps): Promise<Metadata> {
  const { id } = await params
  const document = await getPublicDocument(id)

  if (!document) {
    return {
      title: 'Document Not Found',
      description: 'The requested document could not be found or is not publicly accessible.',
    }
  }

  return {
    title: document.title || 'Shared Document',
    description: 'A publicly shared document',
    robots: {
      index: false, // Don't index shared documents
      follow: false,
    },
  }
}

export default async function PublicDocumentPage({ params }: PublicDocumentPageProps) {
  const { id } = await params
  const document = await getPublicDocument(id)

  if (!document) {
    notFound()
  }

  // Extract citations from metadata if available
  const citations = (document.metadata as any)?.citations || []

  return (
    <PublicDocumentViewer
      title={document.title || undefined}
      content={(document.initial_content || document.content) || undefined}
      data={document.data || undefined}
      citations={citations}
    />
  )
}
