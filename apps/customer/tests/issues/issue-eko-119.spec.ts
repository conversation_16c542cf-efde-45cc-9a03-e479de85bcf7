import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-119: Share Panel Issues', () => {
  test('Create SDG Report, share publicly, and verify no 404', async ({ page, context }) => {
    let testUtils: TestUtils
    // Set generous timeout for this test as document loading takes time
    test.setTimeout(180000) // 3 minutes

    // Initialize TestUtils
    testUtils = new TestUtils(page)

    // Login with demo credentials
    await testUtils.login('<EMAIL>', 'demo')

    // Create a new document
    await testUtils.createDocumentFromTemplate('Blank Document')

    // Add some content to make it an SDG Report
    await testUtils.fillEditor('# SDG Impact Report\n\nThis is a test SDG report for EKO-119.')

    // Wait for 2 minutes as specified in the ticket
    console.log('Waiting 2 minutes for document to process...')
    await page.waitForTimeout(120000) // 2 minutes

    // Click the Share button in the collaboration toolbar
    await page.click('button:has-text("Share")')
    
    // Wait for the share panel to open
    await page.waitForSelector('text=Share Document', { timeout: 10000 })

    // Verify the share panel is full width (not w-80 which is 320px)
    const sharePanel = page.locator('div:has(h3:text("Share Document"))').first()
    await expect(sharePanel).toBeVisible()

    // Check that the share panel uses full width class
    const sharePanelContainer = sharePanel.locator('..').first()
    const classes = await sharePanelContainer.getAttribute('class')
    expect(classes).toContain('w-full')
    expect(classes).not.toContain('w-80')

    // Verify the share link input is visible and clickable
    const shareInput = page.locator('input[readonly]').first()
    await expect(shareInput).toBeVisible()
    
    // Test that the share link is clickable
    await shareInput.click()
    
    // Verify copy button is visible
    const copyButton = page.locator('button[title="Copy link to clipboard"]')
    await expect(copyButton).toBeVisible()

    // Make sure access is set to Public
    const publicToggle = page.locator('button:has-text("Private"), button:has-text("Public")')
    await expect(publicToggle).toBeVisible()
    
    // If it's currently private, click to make it public
    const toggleText = await publicToggle.textContent()
    if (toggleText?.includes('Private')) {
      await publicToggle.click()
      await page.waitForTimeout(2000) // Wait for the update to complete
    }

    // Verify it's now public
    await expect(page.locator('button:has-text("Public")')).toBeVisible()

    // Get the share link
    const shareLink = await shareInput.inputValue()
    console.log('Share link:', shareLink)

    // Verify the share link format is correct for public documents
    expect(shareLink).toMatch(/\/share\/public\/documents\/[a-f0-9-]+/)

    // Use the copy button to get the link and then navigate to it
    await copyButton.click()
    
    // Wait a moment for the copy to complete
    await page.waitForTimeout(1000)
    
    // Open the share link in a new page
    const newPage = await context.newPage()
    await newPage.goto(shareLink, { timeout: 30000 })

    // Verify we don't get a 404
    await newPage.waitForLoadState('networkidle', { timeout: 30000 })
    
    // Check that we're not on a 404 page
    const pageContent = await newPage.textContent('body')
    expect(pageContent).not.toContain('404')
    expect(pageContent).not.toContain('Not Found')
    expect(pageContent).not.toContain('Document Not Found')

    // Verify the document content is visible
    await expect(newPage.locator('text=SDG Impact Report')).toBeVisible({ timeout: 10000 })
    await expect(newPage.locator('text=This is a test SDG report for EKO-119')).toBeVisible()

    // Verify it's the public document viewer (should have specific elements)
    await expect(newPage.locator('text=This document was shared publicly')).toBeVisible()

    // Clean up
    await newPage.close()

    console.log('✅ Test completed successfully - no 404 error when accessing public share link')
  })

  test('Share panel UI improvements', async ({ page }) => {
    // Test the UI improvements separately
    test.setTimeout(60000)
    
    const testUtils = new TestUtils(page)

    // Login and create a document
    await testUtils.login('<EMAIL>', 'demo')
    await testUtils.createDocumentFromTemplate('Blank Document')

    // Open share panel
    await page.click('button:has-text("Share")')
    await page.waitForSelector('text=Share Document', { timeout: 10000 })

    // Test share panel width
    const sharePanel = page.locator('div:has(h3:text("Share Document"))').first()
    const sharePanelContainer = sharePanel.locator('..').first()
    const classes = await sharePanelContainer.getAttribute('class')
    
    // Verify it uses w-full instead of w-80
    expect(classes).toContain('w-full')
    expect(classes).not.toContain('w-80')

    // Test share link clickability
    const shareInput = page.locator('input[readonly]').first()
    await expect(shareInput).toBeVisible()
    await expect(shareInput).toHaveClass(/cursor-pointer/)

    // Test copy button visibility and functionality
    const copyButton = page.locator('button[title="Copy link to clipboard"]')
    await expect(copyButton).toBeVisible()
    
    // Click copy button and verify it works
    await copyButton.click()
    
    // Check for success feedback (icon should change to check mark)
    await expect(page.locator('button[title="Copy link to clipboard"] svg')).toBeVisible()

    console.log('✅ Share panel UI improvements verified')
  })
})
