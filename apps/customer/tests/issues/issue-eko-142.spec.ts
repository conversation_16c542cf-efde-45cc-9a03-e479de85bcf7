import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('Issue EKO-142: Fixed Editor AI Features Test', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should test AI features with exact selectors and no conditional logic - EKO-142', async ({ page }) => {
    // This test demonstrates the corrected fix for EKO-142 by testing actual AI features
    // using exact selectors without conditional logic, assuming all features are available
    
    await testUtils.createDocumentFromTemplate()
    
    // Wait for editor to load
    await testUtils.waitForEditor()
    
    // Test 1: AI toolbar should be visible with all buttons
    await expect(page.locator('[data-testid="ai-toolbar"]')).toBeVisible()
    await expect(page.locator('button[title="Improve Writing"]')).toBeVisible()
    await expect(page.locator('button[title="Fix Grammar"]')).toBeVisible()
    await expect(page.locator('button[title="AI Chat"]')).toBeVisible()
    
    // Test 2: Test AI command with text selection
    await testUtils.typeInEditor('Sample text for testing AI features')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Mock API response
    await testUtils.mockApiResponse('/api/ai/generate', {
      result: 'Improved sample text for testing AI features',
      success: true
    })
    
    // Test Improve Writing command
    await page.click('button[title="Improve Writing"]')
    
    // Should show processing indicator
    await expect(page.locator('[data-testid="ai-processing"]')).toBeVisible({ timeout: 2000 })
    
    // Wait for processing to complete
    await expect(page.locator('[data-testid="ai-processing"]')).not.toBeVisible({ timeout: 10000 })
    
    // Test 3: Test AI chat functionality
    await page.click('button[title="AI Chat"]')
    await expect(page.locator('[data-testid="ai-chat-panel"]')).toBeVisible()
    await expect(page.locator('input[placeholder*="Ask the AI assistant"]')).toBeVisible()
    
    // Close chat panel
    await page.click('button[aria-label="Close"]')
    await expect(page.locator('[data-testid="ai-chat-panel"]')).not.toBeVisible()
    
    // Test 4: Test slash commands
    await page.keyboard.press('Enter')
    await page.keyboard.type('/ai')
    
    // Should show AI commands dropdown
    await expect(page.locator('.z-50.min-w-\\[280px\\].max-w-\\[320px\\]')).toBeVisible({ timeout: 5000 })
    await expect(page.locator('button:has-text("Improve Writing")')).toBeVisible()
    
    // Dismiss slash command
    await page.keyboard.press('Escape')
  })

  test('should handle API errors gracefully - EKO-142', async ({ page }) => {
    // This test shows proper error handling with exact selectors
    
    await testUtils.createDocumentFromTemplate()
    await testUtils.waitForEditor()
    
    // Mock AI API errors
    await page.route('**/api/ai/**', route => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'AI service unavailable' })
      })
    })
    
    // Add content and test AI command error handling
    await testUtils.typeInEditor('Test content for error handling')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Try AI command - should handle error gracefully
    await page.click('button[title="Improve Writing"]')
    
    // Wait for error handling
    await page.waitForTimeout(2000)
    
    // Editor should still be functional after error
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible()
    
    // Test chat panel error handling
    await page.click('button[title="AI Chat"]')
    await expect(page.locator('[data-testid="ai-chat-panel"]')).toBeVisible()
    
    const chatInput = page.locator('input[placeholder*="Ask the AI assistant"]')
    await chatInput.fill('Test message')
    
    await page.click('button:has(svg[data-lucide="send"])')
    await page.waitForTimeout(2000)
    
    // Close panel
    await page.click('button[aria-label="Close"]')
    await expect(page.locator('[data-testid="ai-chat-panel"]')).not.toBeVisible()
    
    // Verify editor is still functional after error scenarios
    await testUtils.typeInEditor(' More content after errors')
    await testUtils.checkEditorContent('More content after errors')
  })

  test('should use proper test utilities with exact selectors - EKO-142', async ({ page }) => {
    // This test demonstrates using TestUtils methods with exact selectors
    
    const documentId = await testUtils.createDocumentFromTemplate()
    expect(documentId).toBeTruthy()
    
    // Use TestUtils methods for common operations
    await testUtils.typeInEditor('Content added via TestUtils')
    await testUtils.checkEditorContent('Content added via TestUtils')
    
    // Test that all AI features are accessible
    await expect(page.locator('[data-testid="ai-toolbar"]')).toBeVisible()
    await expect(page.locator('button[title="AI Chat"]')).toBeVisible()
    
    // Test navigation
    await testUtils.goToDocuments()
    await page.goto(`/customer/documents/${documentId}`)
    
    // Verify content persisted
    await testUtils.waitForEditor()
    await testUtils.checkEditorContent('Content added via TestUtils')
    
    // Test error checking
    await testUtils.checkForErrors()
  })

  test('should test all required AI features are present - EKO-142', async ({ page }) => {
    // Test that verifies all expected AI features are available for test user
    
    await testUtils.createDocumentFromTemplate()
    await testUtils.waitForEditor()
    
    // All AI features should be available for test user
    await expect(page.locator('[data-testid="ai-toolbar"]')).toBeVisible()
    await expect(page.locator('button[title="Improve Writing"]')).toBeVisible()
    await expect(page.locator('button[title="Fix Grammar"]')).toBeVisible()
    await expect(page.locator('button[title="Make Shorter"]')).toBeVisible()
    await expect(page.locator('button[title="Expand"]')).toBeVisible()
    await expect(page.locator('button[title="Change Tone"]')).toBeVisible()
    await expect(page.locator('button[title="Summarize"]')).toBeVisible()
    await expect(page.locator('button[title="Continue Writing"]')).toBeVisible()
    await expect(page.locator('button[title="Brainstorm Ideas"]')).toBeVisible()
    await expect(page.locator('button[title="AI Chat"]')).toBeVisible()
    
    // Test that core functionality works
    await testUtils.typeInEditor('All AI features are available and working')
    await testUtils.checkEditorContent('All AI features are available and working')
    
    // Verify document can be saved and loaded
    const documentId = testUtils.getDocumentIdFromUrl()
    expect(documentId).toBeTruthy()
  })
})
