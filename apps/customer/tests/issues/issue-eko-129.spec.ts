import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-129: Collaboration Features', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display collaboration toolbar when editor loads', async ({ page }) => {
    // Navigate to a document page directly (bypassing template creation)
    await page.goto('/customer/documents/test-doc-id')
    
    // Wait for page to load
    await page.waitForLoadState('networkidle')
    
    // Check if collaboration toolbar is visible
    const collaborationToolbar = page.locator('[data-testid="collaboration-toolbar"]')
    await expect(collaborationToolbar).toBeVisible({ timeout: 10000 })
    
    // Check collaboration buttons exist
    await expect(page.locator('button[title*="Comments"]')).toBeVisible()
    await expect(page.locator('button[title*="History"]')).toBeVisible()
    await expect(page.locator('button[title*="Share"]')).toBeVisible()
    await expect(page.locator('button[title*="Settings"]')).toBeVisible()
  })

  test('should open and close comments panel', async ({ page }) => {
    await page.goto('/customer/documents/test-doc-id')
    await page.waitForLoadState('networkidle')
    
    // Open comments panel
    await page.click('button[title*="Comments"]')
    
    // Check comments panel opens
    await expect(page.locator('[data-testid="comments-panel"]')).toBeVisible()
    
    // Check panel content
    await expect(page.locator('text=Comments')).toBeVisible()
    
    // Close panel by clicking comments button again
    await page.click('button[title*="Comments"]')
    await expect(page.locator('[data-testid="comments-panel"]')).not.toBeVisible()
  })

  test('should open and close history panel', async ({ page }) => {
    await page.goto('/customer/documents/test-doc-id')
    await page.waitForLoadState('networkidle')
    
    // Open history panel
    await page.click('button[title*="History"]')
    
    // Check history panel opens
    await expect(page.locator('[data-testid="history-panel"]')).toBeVisible()
    
    // Check panel content
    await expect(page.locator('text=Document History')).toBeVisible()
    
    // Close panel
    await page.click('button[title*="History"]')
    await expect(page.locator('[data-testid="history-panel"]')).not.toBeVisible()
  })

  test('should open and close share panel', async ({ page }) => {
    await page.goto('/customer/documents/test-doc-id')
    await page.waitForLoadState('networkidle')
    
    // Open share panel
    await page.click('button[title*="Share"]')
    
    // Check share panel opens
    await expect(page.locator('[data-testid="share-panel"]')).toBeVisible()
    
    // Check panel content
    await expect(page.locator('text=Share Document')).toBeVisible()
    await expect(page.locator('input[placeholder*="email"]')).toBeVisible()
    await expect(page.locator('button:has-text("Send Invite")')).toBeVisible()
    
    // Close panel by clicking share button again
    await page.click('button[title*="Share"]')
    await expect(page.locator('[data-testid="share-panel"]')).not.toBeVisible()
  })

  test('should open collaboration settings dialog', async ({ page }) => {
    await page.goto('/customer/documents/test-doc-id')
    await page.waitForLoadState('networkidle')
    
    // Click settings button
    await page.click('button[title*="Settings"]')
    
    // Should show settings dialog
    await expect(page.locator('[data-testid="collaboration-settings"]')).toBeVisible()
    
    // Should have permission settings
    await expect(page.locator('text=Document Permissions')).toBeVisible()
    await expect(page.locator('text=Public')).toBeVisible()
    await expect(page.locator('text=Private')).toBeVisible()
    
    // Should have notification settings
    await expect(page.locator('text=Notifications')).toBeVisible()
    await expect(page.locator('input[type="checkbox"]')).toBeVisible()
    
    // Close dialog
    await page.click('button:has-text("Cancel")')
    await expect(page.locator('[data-testid="collaboration-settings"]')).not.toBeVisible()
  })

  test('should show collaborator avatars', async ({ page }) => {
    await page.goto('/customer/documents/test-doc-id')
    await page.waitForLoadState('networkidle')
    
    // Check for collaborator avatars in toolbar
    await expect(page.locator('[data-testid="collaborator-avatar"]')).toBeVisible()
    
    // Should show current user
    await expect(page.locator('[data-testid="current-user-avatar"]')).toBeVisible()
    
    // Hover over avatar to see user info
    await page.hover('[data-testid="current-user-avatar"]')
    await expect(page.locator('[data-testid="user-tooltip"]')).toBeVisible({ timeout: 2000 })
  })

  test('should handle share invitations', async ({ page }) => {
    await page.goto('/customer/documents/test-doc-id')
    await page.waitForLoadState('networkidle')
    
    // Open share panel
    await page.click('button[title*="Share"]')
    await expect(page.locator('[data-testid="share-panel"]')).toBeVisible()
    
    // Enter email
    await page.fill('input[placeholder*="email"]', '<EMAIL>')
    
    // Select permission level
    await page.click('[data-testid="permission-select"]')
    await page.click('text=Can edit')
    
    // Send invite (this might fail due to backend, but UI should work)
    await page.click('button:has-text("Send Invite")')
    
    // Should show some response (success or error)
    // We'll just check that the form is still there
    await expect(page.locator('input[placeholder*="email"]')).toBeVisible()
  })

  test('should display version history items', async ({ page }) => {
    await page.goto('/customer/documents/test-doc-id')
    await page.waitForLoadState('networkidle')
    
    // Open history panel
    await page.click('button[title*="History"]')
    await expect(page.locator('[data-testid="history-panel"]')).toBeVisible()
    
    // Should show version items (even if empty state)
    const versionItems = page.locator('[data-testid="version-item"]')
    const versionCount = await versionItems.count()
    
    if (versionCount > 0) {
      // If versions exist, check they have timestamps
      await expect(page.locator('[data-testid="version-timestamp"]')).toHaveCount(versionCount)
    } else {
      // If no versions, should show empty state
      await expect(page.locator('text=No version history')).toBeVisible()
    }
  })

  test('should handle comment functionality', async ({ page }) => {
    await page.goto('/customer/documents/test-doc-id')
    await page.waitForLoadState('networkidle')
    
    // Open comments panel
    await page.click('button[title*="Comments"]')
    await expect(page.locator('[data-testid="comments-panel"]')).toBeVisible()
    
    // Check for add comment functionality
    await expect(page.locator('button:has-text("Add Comment")')).toBeVisible()
    
    // Check for comment form
    await expect(page.locator('textarea[placeholder*="comment"]')).toBeVisible()
    
    // Try to add a comment (might fail due to backend)
    await page.fill('textarea[placeholder*="comment"]', 'This is a test comment')
    await page.click('button:has-text("Add Comment")')
    
    // Should show some response or keep the form visible
    await expect(page.locator('textarea[placeholder*="comment"]')).toBeVisible()
  })
})
