import { test, expect } from '@playwright/test'

/**
 * Test for EKO-130: Auto Saving
 * 
 * Tests that:
 * 1. When a report section finishes loading, the document should be saved
 * 2. When all report components load, an automatic save version should be created
 * 3. Auto-saves should be marked as "Automatic Save" not "Manual Save"
 */

test.describe('EKO-130: Auto Saving', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the authentication
    await page.route('**/auth/getUser', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: {
            user: {
              id: 'test-user-id',
              email: '<EMAIL>',
              name: 'Test User'
            }
          }
        })
      })
    })

    // Mock Supabase operations for document saving and versioning
    await page.route('**/rest/v1/collaborative_documents*', async (route) => {
      if (route.request().method() === 'PATCH') {
        // Mock document update (auto-save)
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({})
        })
      } else {
        await route.continue()
      }
    })

    await page.route('**/rest/v1/document_versions*', async (route) => {
      if (route.request().method() === 'POST') {
        // Mock version creation
        const requestBody = await route.request().postDataJSON()
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'test-version-id',
            version_number: requestBody.version_number,
            is_auto_save: requestBody.is_auto_save,
            change_summary: requestBody.change_summary
          })
        })
      } else if (route.request().method() === 'GET') {
        // Mock version listing for getting next version number
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([{ version_number: 1 }])
        })
      } else {
        await route.continue()
      }
    })

    // Mock report API endpoints
    await page.route('**/api/report/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          text: 'Sample report section content',
          citations: []
        })
      })
    })
  })

  test('should auto-save when individual report section finishes loading', async ({ page }) => {
    // Track auto-save requests
    const autoSaveRequests: any[] = []
    
    page.on('request', (request) => {
      if (request.url().includes('/collaborative_documents') && request.method() === 'PATCH') {
        autoSaveRequests.push({
          url: request.url(),
          method: request.method(),
          timestamp: Date.now()
        })
      }
    })

    // Navigate to editor with a simple document containing a report section
    await page.goto('/customer/documents/test-doc?mode=edit')

    // Wait for editor to load
    await page.waitForSelector('[data-testid="editor-content"]', { timeout: 10000 })

    // Insert a report section (simulating existing document content)
    await page.evaluate(() => {
      const editor = (window as any).testEditor
      if (editor) {
        editor.commands.insertContent({
          type: 'reportSection',
          attrs: {
            id: 'test-section',
            title: 'Test Section',
            endpoint: '/report/test-endpoint',
            status: 'idle'
          }
        })
      }
    })

    // Trigger report section loading by setting entity/run
    await page.evaluate(() => {
      const documentContext = (window as any).testDocumentContext
      if (documentContext) {
        documentContext.setEntityRun('test-entity', 'test-run')
      }
    })

    // Wait for the report section to start loading and then complete
    await page.waitForFunction(() => {
      const documentContext = (window as any).testDocumentContext
      if (!documentContext) return false
      
      const component = documentContext.state.components.get('test-section')
      return component && component.status === 'loaded'
    }, { timeout: 10000 })

    // Verify that auto-save was triggered when the section finished loading
    await page.waitForFunction(() => autoSaveRequests.length > 0, { timeout: 5000 })
    
    expect(autoSaveRequests.length).toBeGreaterThan(0)
  })

  test('should create auto-save version when all components are loaded', async ({ page }) => {
    // Track version creation requests
    const versionRequests: any[] = []
    
    page.on('request', async (request) => {
      if (request.url().includes('/document_versions') && request.method() === 'POST') {
        const body = await request.postDataJSON().catch(() => ({}))
        versionRequests.push({
          url: request.url(),
          method: request.method(),
          body: body,
          timestamp: Date.now()
        })
      }
    })

    // Navigate to editor
    await page.goto('/customer/documents/test-doc?mode=edit')
    await page.waitForSelector('[data-testid="editor-content"]', { timeout: 10000 })

    // Insert multiple report components
    await page.evaluate(() => {
      const editor = (window as any).testEditor
      if (editor) {
        // Insert report group with two sections
        editor.commands.insertContent([
          {
            type: 'reportGroup',
            attrs: {
              id: 'test-group',
              title: 'Test Group',
              status: 'idle'
            },
            content: [
              {
                type: 'reportSection',
                attrs: {
                  id: 'test-section-1',
                  title: 'Test Section 1',
                  endpoint: '/report/test-endpoint-1',
                  status: 'idle',
                  parentId: 'test-group'
                }
              },
              {
                type: 'reportSection',
                attrs: {
                  id: 'test-section-2',
                  title: 'Test Section 2',
                  endpoint: '/report/test-endpoint-2',
                  status: 'idle',
                  parentId: 'test-group'
                }
              }
            ]
          }
        ])
      }
    })

    // Trigger loading for all components
    await page.evaluate(() => {
      const documentContext = (window as any).testDocumentContext
      if (documentContext) {
        documentContext.setEntityRun('test-entity', 'test-run')
      }
    })

    // Wait for all components to reach loaded state
    await page.waitForFunction(() => {
      const documentContext = (window as any).testDocumentContext
      if (!documentContext) return false
      
      return documentContext.areAllComponentsLoaded()
    }, { timeout: 15000 })

    // Verify that an auto-save version was created
    await page.waitForFunction(() => 
      versionRequests.some(req => 
        req.body?.is_auto_save === true && 
        req.body?.change_summary?.includes('All report components loaded')
      ), 
      { timeout: 5000 }
    )

    const autoSaveVersionRequest = versionRequests.find(req => 
      req.body?.is_auto_save === true
    )
    
    expect(autoSaveVersionRequest).toBeDefined()
    expect(autoSaveVersionRequest.body.is_auto_save).toBe(true)
    expect(autoSaveVersionRequest.body.change_summary).toContain('All report components loaded')
    expect(autoSaveVersionRequest.body.change_summary).toContain('Automatic save')
  })

  test('should distinguish between manual and automatic saves', async ({ page }) => {
    const saveRequests: any[] = []
    const versionRequests: any[] = []
    
    // Track both regular saves and version creation
    page.on('request', async (request) => {
      if (request.url().includes('/collaborative_documents') && request.method() === 'PATCH') {
        saveRequests.push({
          type: 'document_save',
          timestamp: Date.now()
        })
      }
      
      if (request.url().includes('/document_versions') && request.method() === 'POST') {
        const body = await request.postDataJSON().catch(() => ({}))
        versionRequests.push({
          type: 'version_creation',
          body: body,
          timestamp: Date.now()
        })
      }
    })

    await page.goto('/customer/documents/test-doc?mode=edit')
    await page.waitForSelector('[data-testid="editor-content"]', { timeout: 10000 })

    // Trigger a manual save first
    await page.click('[data-testid="save-button"]')
    
    // Insert and load a report section to trigger auto-save
    await page.evaluate(() => {
      const editor = (window as any).testEditor
      if (editor) {
        editor.commands.insertContent({
          type: 'reportSection',
          attrs: {
            id: 'auto-save-test-section',
            title: 'Auto Save Test Section',
            endpoint: '/report/auto-save-test',
            status: 'idle'
          }
        })
      }
    })

    await page.evaluate(() => {
      const documentContext = (window as any).testDocumentContext
      if (documentContext) {
        documentContext.setEntityRun('test-entity', 'test-run')
      }
    })

    // Wait for section to load and auto-save to trigger
    await page.waitForFunction(() => {
      const documentContext = (window as any).testDocumentContext
      if (!documentContext) return false
      
      const component = documentContext.state.components.get('auto-save-test-section')
      return component && component.status === 'loaded'
    }, { timeout: 10000 })

    // Wait for auto-save version to be created
    await page.waitForFunction(() => 
      versionRequests.some(req => req.body?.is_auto_save === true), 
      { timeout: 5000 }
    )

    // Verify we have both manual and automatic saves
    const manualVersions = versionRequests.filter(req => req.body?.is_auto_save === false)
    const autoVersions = versionRequests.filter(req => req.body?.is_auto_save === true)
    
    expect(manualVersions.length).toBeGreaterThan(0)
    expect(autoVersions.length).toBeGreaterThan(0)
    
    // Verify change summaries are different
    if (manualVersions.length > 0) {
      expect(manualVersions[0].body.change_summary).not.toContain('Automatic save')
    }
    
    if (autoVersions.length > 0) {
      expect(autoVersions[0].body.change_summary).toContain('Automatic save')
    }
  })

  test('should handle multiple components loading in sequence', async ({ page }) => {
    const versionRequests: any[] = []
    
    page.on('request', async (request) => {
      if (request.url().includes('/document_versions') && request.method() === 'POST') {
        const body = await request.postDataJSON().catch(() => ({}))
        versionRequests.push({
          body: body,
          timestamp: Date.now()
        })
      }
    })

    await page.goto('/customer/documents/test-doc?mode=edit')
    await page.waitForSelector('[data-testid="editor-content"]', { timeout: 10000 })

    // Insert multiple report sections
    await page.evaluate(() => {
      const editor = (window as any).testEditor
      if (editor) {
        editor.commands.insertContent([
          {
            type: 'reportSection',
            attrs: {
              id: 'seq-section-1',
              title: 'Sequential Section 1',
              endpoint: '/report/seq-1',
              status: 'idle'
            }
          },
          {
            type: 'reportSection',
            attrs: {
              id: 'seq-section-2',
              title: 'Sequential Section 2',
              endpoint: '/report/seq-2',
              status: 'idle'
            }
          },
          {
            type: 'reportSection',
            attrs: {
              id: 'seq-section-3',
              title: 'Sequential Section 3',
              endpoint: '/report/seq-3',
              status: 'idle'
            }
          }
        ])
      }
    })

    // Trigger loading
    await page.evaluate(() => {
      const documentContext = (window as any).testDocumentContext
      if (documentContext) {
        documentContext.setEntityRun('test-entity', 'test-run')
      }
    })

    // Wait for all sections to load
    await page.waitForFunction(() => {
      const documentContext = (window as any).testDocumentContext
      if (!documentContext) return false
      
      const section1 = documentContext.state.components.get('seq-section-1')
      const section2 = documentContext.state.components.get('seq-section-2')
      const section3 = documentContext.state.components.get('seq-section-3')
      
      return section1?.status === 'loaded' && 
             section2?.status === 'loaded' && 
             section3?.status === 'loaded'
    }, { timeout: 20000 })

    // Should have created only ONE auto-save version when all components are loaded
    await page.waitForFunction(() => 
      versionRequests.some(req => 
        req.body?.is_auto_save === true && 
        req.body?.change_summary?.includes('All report components loaded')
      ), 
      { timeout: 5000 }
    )

    const autoSaveVersions = versionRequests.filter(req => 
      req.body?.is_auto_save === true && 
      req.body?.change_summary?.includes('All report components loaded')
    )
    
    // Should have exactly one "all components loaded" auto-save version
    expect(autoSaveVersions.length).toBe(1)
  })
})