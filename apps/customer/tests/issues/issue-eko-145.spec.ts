import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('Issue EKO-145: AI Slash Commands', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should show AI Commands option in main slash menu', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Click in editor and type slash command
    await editor.click()
    await page.keyboard.type('/')

    // Should show AI Commands as an option in the main menu
    await expect(page.locator('text=AI Commands')).toBeVisible()
    await expect(page.locator('text=Access AI-powered writing tools')).toBeVisible()
  })

  test('should trigger AI submenu when clicking AI Commands from slash menu', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Click in editor and type slash command
    await editor.click()
    await page.keyboard.type('/')

    // Click on AI Commands option
    await page.click('text=AI Commands')

    // Should show AI commands submenu
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-improve"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-grammar"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-shorter"]')).toBeVisible()
  })

  test('should dismiss AI menu when clicking outside', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Trigger AI commands menu
    await editor.click()
    await page.keyboard.type('/ai')

    // Verify menu is visible
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible()

    // Click outside the menu (on the editor background)
    await editor.click({ position: { x: 100, y: 100 } })

    // Menu should be dismissed
    await expect(page.locator('[data-testid="ai-commands-header"]')).not.toBeVisible()
  })

  test('should properly execute AI commands and remove slash text', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Add some text first
    await testUtils.typeInEditor('Test text for AI improvement')
    await page.keyboard.press('Enter')

    // Type AI slash command
    await page.keyboard.type('/ai')

    // Should show AI commands menu
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible()

    // Mock AI API response
    await testUtils.mockApiResponse('/api/ai/chat', {
      type: 'chat',
      message: {
        role: 'assistant', 
        content: 'Improved text content',
        timestamp: new Date().toISOString()
      },
      success: true
    })

    // Click on improve command
    await page.click('[data-testid="ai-slash-command-improve"]')

    // The /ai text should be removed from the editor
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).not.toContainText('/ai')
    
    // Should not crash or show error
    await page.waitForTimeout(1000)
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).toBeVisible()
  })

  test('should handle AI command menu keyboard navigation', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Should show AI commands menu
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible()

    // Test keyboard navigation
    await page.keyboard.press('ArrowDown')
    await page.keyboard.press('ArrowDown')
    await page.keyboard.press('ArrowUp')

    // Should be able to press Enter to select
    await page.keyboard.press('Enter')

    // The /ai text should be removed
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).not.toContainText('/ai')
  })

  test('should handle Escape key to dismiss AI menu', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Should show AI commands menu
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible()

    // Press Escape to dismiss
    await page.keyboard.press('Escape')

    // Menu should be dismissed
    await expect(page.locator('[data-testid="ai-commands-header"]')).not.toBeVisible()
    
    // The /ai text should still be there (not executed)
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).toContainText('/ai')
  })
})