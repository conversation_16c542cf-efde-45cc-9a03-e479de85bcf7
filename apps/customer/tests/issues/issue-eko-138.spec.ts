import { test, expect } from '@playwright/test'
import { login } from '../helpers/auth'

test.describe('EKO-138: On Document Creation Hide List', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await login(page)
    
    // Navigate to documents page
    await page.goto('/customer/documents')
    
    // Wait for the page to load
    await expect(page.locator('h1')).toContainText('Documents')
  })

  test('should hide document list and show loading state when New Document is clicked', async ({ page }) => {
    // First, just check if the template dialog opens at all
    const newDocButton = page.getByRole('button', { name: 'New Document' }).first()
    await expect(newDocButton).toBeVisible()
    await newDocButton.click()

    // Wait for dialog to open
    await expect(page.locator('[data-testid="template-dialog"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('text=Choose a Template')).toBeVisible()

    // Now check if the loading state is shown 
    await expect(page.locator('[data-testid="document-creation-loading"]')).toBeVisible()
    await expect(page.locator('text=Creating document...')).toBeVisible()
    await expect(page.locator('text=Please select a template to continue')).toBeVisible()

    // Verify documents list is hidden
    const documentsGrid = page.locator('[data-testid="documents-list"]')
    const emptyState = page.locator('text=No documents yet')
    await expect(documentsGrid).not.toBeVisible()
    await expect(emptyState).not.toBeVisible()
  })

  test('should restore document list when template dialog is closed', async ({ page }) => {
    // Click New Document button to trigger creation state
    await page.getByRole('button', { name: /new document/i }).click()

    // Verify loading state is shown
    await expect(page.locator('[data-testid="document-creation-loading"]')).toBeVisible()

    // Close the dialog by clicking Cancel
    await page.getByRole('button', { name: /cancel/i }).click()

    // Verify loading state is hidden
    await expect(page.locator('[data-testid="document-creation-loading"]')).not.toBeVisible()

    // Verify documents list is restored (either with docs or empty state)
    const documentsGrid = page.locator('[data-testid="documents-list"]')
    const emptyState = page.locator('text=No documents yet')
    
    await expect(
      documentsGrid.or(emptyState)
    ).toBeVisible()

    // Verify template dialog is closed
    await expect(page.locator('[data-testid="template-dialog"]')).not.toBeVisible()
  })

  test('should restore document list when template dialog is closed by escape key', async ({ page }) => {
    // Click New Document button to trigger creation state
    await page.getByRole('button', { name: /new document/i }).click()

    // Verify loading state is shown
    await expect(page.locator('[data-testid="document-creation-loading"]')).toBeVisible()

    // Close the dialog by pressing Escape
    await page.keyboard.press('Escape')

    // Wait a bit for the dialog to close
    await page.waitForTimeout(100)

    // Verify loading state is hidden
    await expect(page.locator('[data-testid="document-creation-loading"]')).not.toBeVisible()

    // Verify documents list is restored
    const documentsGrid = page.locator('[data-testid="documents-list"]')
    const emptyState = page.locator('text=No documents yet')
    
    const isDocumentsGridVisible = await documentsGrid.isVisible()
    const isEmptyStateVisible = await emptyState.isVisible()
    expect(isDocumentsGridVisible || isEmptyStateVisible).toBeTruthy()

    // Verify template dialog is closed
    await expect(page.locator('[data-testid="template-dialog"]')).not.toBeVisible()
  })

  test('should navigate to document page when template is selected', async ({ page }) => {
    // Click New Document button
    await page.getByRole('button', { name: /new document/i }).click()

    // Verify loading state is shown
    await expect(page.locator('[data-testid="document-creation-loading"]')).toBeVisible()

    // Select the blank document template
    await page.locator('text=Blank Document').click()

    // Wait for navigation to document page
    await expect(page).toHaveURL(/\/customer\/documents\/[^\/]+$/)

    // Verify we're on the document editor page
    await expect(page.locator('text=Back')).toBeVisible()
  })

  test('should show appropriate loading message during document creation', async ({ page }) => {
    // Click New Document button
    await page.getByRole('button', { name: /new document/i }).click()

    // Verify specific loading messages are shown
    await expect(page.locator('text=Creating document...')).toBeVisible()
    await expect(page.locator('text=Please select a template to continue')).toBeVisible()

    // Verify loading spinner is present
    await expect(page.locator('[data-testid="document-creation-loading"] .animate-spin')).toBeVisible()
  })
})