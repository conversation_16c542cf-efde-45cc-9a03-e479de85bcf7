import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-125: Columns Extension - CSS Grid Layout Fix', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()

    // Navigate to a document editor page
    await page.goto('/customer/documents/new')

    // Wait for editor to load
    await testUtils.waitForEditor()
  })

  // Helper function to insert columns via slash command
  async function insertColumnsViaSlash(page: any, slashCommand: string, buttonText: string) {
    await testUtils.typeInEditorAtCursor(slashCommand)
    await page.waitForSelector(`[role="button"]:has-text("${buttonText}")`, { timeout: 5000 })
    await page.click(`[role="button"]:has-text("${buttonText}")`)
  }

  // Helper function to insert and verify column layout
  async function insertAndVerifyColumns(page: any, slashCommand: string, buttonText: string, layoutAttr: string, expectedCount: number) {
    await insertColumnsViaSlash(page, slashCommand, buttonText)
    await expect(page.locator(`.column-block[data-layout="${layoutAttr}"]`)).toBeVisible()
    await expect(page.locator(`.column-block[data-layout="${layoutAttr}"] .column`)).toHaveCount(expectedCount)
  }

  test('should display columns horizontally with proper CSS Grid layout', async ({ page }) => {
    // Insert 3 columns to test the specific issue
    await insertColumnsViaSlash(page, '/3 columns', '3 Columns')

    // Wait for columns to be inserted
    await page.waitForSelector('.column-block[data-layout="equal-3"]', { timeout: 10000 })

    // Check that the column block exists
    const columnBlock = page.locator('.column-block[data-layout="equal-3"]')
    await expect(columnBlock).toBeVisible()

    // Check that the grid container has the correct CSS properties
    const gridContainer = page.locator('.column-block[data-layout="equal-3"] [data-node-view-content-react]')
    await expect(gridContainer).toBeVisible()

    // Verify CSS Grid properties are applied correctly
    const computedStyle = await gridContainer.evaluate((el) => {
      const style = window.getComputedStyle(el)
      return {
        display: style.display,
        gridTemplateColumns: style.gridTemplateColumns,
        gap: style.gap,
        gridAutoFlow: style.gridAutoFlow
      }
    })

    expect(computedStyle.display).toBe('grid')
    expect(computedStyle.gridTemplateColumns).toBe('1fr 1fr 1fr')
    expect(computedStyle.gap).toBe('24px') // 1.5rem = 24px
    expect(computedStyle.gridAutoFlow).toBe('column')

    // Check that individual columns are present
    const columns = page.locator('.column-block[data-layout="equal-3"] .column')
    await expect(columns).toHaveCount(3)

    // Verify columns are positioned horizontally (side by side)
    const columnBoxes = await columns.all()
    const firstColumnBox = await columnBoxes[0].boundingBox()
    const secondColumnBox = await columnBoxes[1].boundingBox()
    const thirdColumnBox = await columnBoxes[2].boundingBox()

    expect(firstColumnBox).toBeTruthy()
    expect(secondColumnBox).toBeTruthy()
    expect(thirdColumnBox).toBeTruthy()

    // Verify columns are horizontally aligned (same top position, different left positions)
    expect(Math.abs(firstColumnBox!.y - secondColumnBox!.y)).toBeLessThan(5) // Allow small variance
    expect(Math.abs(secondColumnBox!.y - thirdColumnBox!.y)).toBeLessThan(5)
    expect(firstColumnBox!.x).toBeLessThan(secondColumnBox!.x)
    expect(secondColumnBox!.x).toBeLessThan(thirdColumnBox!.x)
  })

  test('should insert 2 equal columns via slash command', async ({ page }) => {
    await insertAndVerifyColumns(page, '/2 columns', '2 Columns', 'equal-2', 2)
  })

  test('should insert 3 equal columns via slash command', async ({ page }) => {
    await insertAndVerifyColumns(page, '/3 columns', '3 Columns', 'equal-3', 3)
  })

  test('should insert 4 equal columns via slash command', async ({ page }) => {
    await insertAndVerifyColumns(page, '/4 columns', '4 Columns', 'equal-4', 4)
  })

  test('should insert 1/3 - 2/3 ratio columns via slash command', async ({ page }) => {
    await insertAndVerifyColumns(page, '/1/3', '1/3 - 2/3 Columns', 'ratio-1-2', 2)
  })

  test('should insert 2/3 - 1/3 ratio columns via slash command', async ({ page }) => {
    await insertAndVerifyColumns(page, '/2/3', '2/3 - 1/3 Columns', 'ratio-2-1', 2)
  })

  test('should insert centered column via slash command', async ({ page }) => {
    await insertAndVerifyColumns(page, '/centered', 'Centered Column', 'centered', 1)
  })

  test('should show hover guidelines when hovering over columns', async ({ page }) => {
    // Insert 2 columns first
    await insertColumnsViaSlash(page, '/2 columns', '2 Columns')
    
    // Wait for column block to be visible
    await expect(page.locator('.column-block[data-layout="equal-2"]')).toBeVisible()
    
    // Hover over the column block
    await page.hover('.column-block[data-layout="equal-2"]')
    
    // Check if hover class is applied
    await expect(page.locator('.column-block.column-block-hovered')).toBeVisible()
  })

  test('should show layout selector on hover', async ({ page }) => {
    // Insert 2 columns first
    await insertColumnsViaSlash(page, '/2 columns', '2 Columns')
    
    // Wait for column block to be visible
    await expect(page.locator('.column-block[data-layout="equal-2"]')).toBeVisible()
    
    // Hover over the column block
    await page.hover('.column-block[data-layout="equal-2"]')
    
    // Check if layout selector buttons are visible
    await expect(page.locator('.column-block button[title="2 Equal Columns"]')).toBeVisible()
    await expect(page.locator('.column-block button[title="3 Equal Columns"]')).toBeVisible()
    await expect(page.locator('.column-block button[title="4 Equal Columns"]')).toBeVisible()
    await expect(page.locator('.column-block button[title="Delete Columns"]')).toBeVisible()
  })

  test('should change column layout using hover controls', async ({ page }) => {
    // Insert 2 columns first
    await insertColumnsViaSlash(page, '/2 columns', '2 Columns')
    
    // Wait for column block to be visible
    await expect(page.locator('.column-block[data-layout="equal-2"]')).toBeVisible()
    
    // Hover over the column block
    await page.hover('.column-block[data-layout="equal-2"]')
    
    // Click on 3 columns button
    await page.click('.column-block button[title="3 Equal Columns"]')
    
    // Verify layout changed to 3 columns
    await expect(page.locator('.column-block[data-layout="equal-3"]')).toBeVisible()
    await expect(page.locator('.column-block[data-layout="equal-3"] .column')).toHaveCount(3)
  })

  test('should delete columns using hover controls', async ({ page }) => {
    // Insert 2 columns first
    await insertColumnsViaSlash(page, '/2 columns', '2 Columns')
    
    // Wait for column block to be visible
    await expect(page.locator('.column-block[data-layout="equal-2"]')).toBeVisible()
    
    // Hover over the column block
    await page.hover('.column-block[data-layout="equal-2"]')
    
    // Click on delete button
    await page.click('.column-block button[title="Delete Columns"]')
    
    // Verify columns are deleted
    await expect(page.locator('.column-block[data-layout="equal-2"]')).not.toBeVisible()
  })

  test('should allow typing in individual columns', async ({ page }) => {
    // Insert 2 columns first
    await insertColumnsViaSlash(page, '/2 columns', '2 Columns')
    
    // Wait for column block to be visible
    await expect(page.locator('.column-block[data-layout="equal-2"]')).toBeVisible()
    
    // Click in the first column and type
    await page.click('.column-block .column:first-child')
    await page.type('.column-block .column:first-child', 'First column content')
    
    // Click in the second column and type
    await page.click('.column-block .column:last-child')
    await page.type('.column-block .column:last-child', 'Second column content')
    
    // Verify content was added to both columns
    await expect(page.locator('.column-block .column:first-child')).toContainText('First column content')
    await expect(page.locator('.column-block .column:last-child')).toContainText('Second column content')
  })

  test('should have minimum height of 2em for drag targets', async ({ page }) => {
    // Insert 2 columns first
    await insertColumnsViaSlash(page, '/2 columns', '2 Columns')
    
    // Wait for column block to be visible
    await expect(page.locator('.column-block[data-layout="equal-2"]')).toBeVisible()
    
    // Check that columns have minimum height
    const columnHeight = await page.locator('.column-block .column:first-child').evaluate(el => {
      return window.getComputedStyle(el).minHeight
    })
    
    expect(columnHeight).toBe('2em')
  })
})
