import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-132: Right Click Context Menu', () => {
  let testUtils: TestUtils
  
  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    
    // Login first
    await testUtils.login()
    
    // Create a new document with the editor
    await testUtils.createDocumentFromTemplate()
    
    // Wait for the editor to be available
    await page.waitForSelector('[data-testid="eko-document-editor"]', { timeout: 10000 })
    await page.waitForSelector('.ProseMirror', { timeout: 5000 })
  })

  test('should show context menu on right click in editor', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click in the editor
    await editor.click({ button: 'right' })
    
    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Should have basic menu items
    await expect(page.locator('text=Select All')).toBeVisible()
    await expect(page.locator('text=Insert')).toBeVisible()
  })

  test('should show clipboard operations when text is selected', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type some text
    await editor.fill('Test text for clipboard operations')
    
    // Select all text
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click on selected text
    await editor.click({ button: 'right' })
    
    // Should show clipboard operations
    await expect(page.locator('text=Copy')).toBeVisible()
    await expect(page.locator('text=Cut')).toBeVisible()
    await expect(page.locator('text=Paste')).toBeVisible()
  })

  test('should show formatting options for selected text', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type and select text
    await editor.fill('Test formatting text')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click
    await editor.click({ button: 'right' })
    
    // Should show format submenu
    await expect(page.locator('text=Format')).toBeVisible()
    
    // Hover over Format to show submenu
    await page.hover('text=Format')
    
    // Should show formatting options
    await expect(page.locator('text=Heading 1')).toBeVisible()
    await expect(page.locator('text=Heading 2')).toBeVisible()
    await expect(page.locator('text=Bullet List')).toBeVisible()
    await expect(page.locator('text=Numbered List')).toBeVisible()
  })

  test('should show comprehensive Insert submenu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click in editor
    await editor.click({ button: 'right' })
    
    // Click Insert menu
    await page.click('text=Insert')
    
    // Should show report-specific items
    await expect(page.locator('text=Table of Contents')).toBeVisible()
    await expect(page.locator('text=Report Summary')).toBeVisible()
    await expect(page.locator('text=Report Section')).toBeVisible()
    await expect(page.locator('text=Report Group')).toBeVisible()
    
    // Should show multi-columns with submenu
    await expect(page.locator('text=Multi-Columns')).toBeVisible()
    
    // Should show basic content types
    await expect(page.locator('text=Table')).toBeVisible()
    await expect(page.locator('text=Chart')).toBeVisible()
    await expect(page.locator('text=Collapsible Section')).toBeVisible()
    await expect(page.locator('text=Code Block')).toBeVisible()
    await expect(page.locator('text=Quote')).toBeVisible()
    await expect(page.locator('text=Math Expression')).toBeVisible()
    await expect(page.locator('text=Horizontal Rule')).toBeVisible()
    await expect(page.locator('text=Image')).toBeVisible()
  })

  test('should show multi-column layout submenu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and navigate to Multi-Columns
    await editor.click({ button: 'right' })
    await page.click('text=Insert')
    
    // Hover over Multi-Columns to show nested submenu
    await page.hover('text=Multi-Columns')
    
    // Should show column layout options
    await expect(page.locator('text=2 Columns (Equal)')).toBeVisible()
    await expect(page.locator('text=3 Columns (Equal)')).toBeVisible()
    await expect(page.locator('text=4 Columns (Equal)')).toBeVisible()
    await expect(page.locator('text=2 Columns (1:2 Ratio)')).toBeVisible()
    await expect(page.locator('text=2 Columns (2:1 Ratio)')).toBeVisible()
    await expect(page.locator('text=Centered Column')).toBeVisible()
  })

  test('should insert table when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and insert table
    await editor.click({ button: 'right' })
    await page.click('text=Insert')
    await page.click('text=Table')
    
    // Should have inserted a table
    await expect(editor.locator('table')).toBeVisible()
    await expect(editor.locator('tr')).toHaveCount(3) // 3 rows by default
    await expect(editor.locator('td, th')).toHaveCount(9) // 3x3 = 9 cells
  })

  test('should insert report section when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and insert report section
    await editor.click({ button: 'right' })
    await page.click('text=Insert')
    await page.click('text=Report Section')
    
    // Should have inserted a report section component
    await expect(editor.locator('[data-type="reportSection"]')).toBeVisible()
  })

  test('should insert chart when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and insert chart
    await editor.click({ button: 'right' })
    await page.click('text=Insert')
    await page.click('text=Chart')
    
    // Should have inserted a chart component
    await expect(editor.locator('[data-type="chart"]')).toBeVisible()
  })

  test('should insert 2-column layout when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and navigate to columns
    await editor.click({ button: 'right' })
    await page.click('text=Insert')
    await page.hover('text=Multi-Columns')
    await page.click('text=2 Columns (Equal)')
    
    // Should have inserted columns
    await expect(editor.locator('[data-type="columns"]')).toBeVisible()
    await expect(editor.locator('[data-type="column"]')).toHaveCount(2)
  })

  test('should show Add Comment option when text is selected and onAddComment is provided', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type and select text
    await editor.fill('Text to comment on')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click
    await editor.click({ button: 'right' })
    
    // Should show Add Comment option
    await expect(page.locator('text=Add Comment')).toBeVisible()
  })

  test('should add link when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type and select text
    await editor.fill('Link text')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click and add link
    await editor.click({ button: 'right' })
    await page.click('text=Add Link')
    
    // Handle the prompt for URL input
    page.on('dialog', async dialog => {
      expect(dialog.message()).toContain('Enter URL')
      await dialog.accept('https://example.com')
    })
    
    // Should have created a link
    await expect(editor.locator('a[href="https://example.com"]')).toBeVisible()
  })

  test('should apply heading formatting when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Type and select text
    await editor.fill('Heading text')
    await page.keyboard.press('ControlOrMeta+a')
    
    // Right click and format as heading
    await editor.click({ button: 'right' })
    await page.hover('text=Format')
    await page.click('text=Heading 1')
    
    // Should have applied heading formatting
    await expect(editor.locator('h1')).toHaveText('Heading text')
  })

  test('should close context menu when clicking elsewhere', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click to show menu
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Click elsewhere to close
    await page.click('body', { position: { x: 100, y: 100 } })
    
    // Menu should be hidden
    await expect(page.locator('[data-testid="right-click-context-menu"]')).not.toBeVisible()
  })

  test('should close context menu when pressing Escape', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click to show menu
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Press Escape to close
    await page.keyboard.press('Escape')
    
    // Menu should be hidden
    await expect(page.locator('[data-testid="right-click-context-menu"]')).not.toBeVisible()
  })

  test('should show AI Suggestions when AI is enabled', async ({ page }) => {
    // Login and create a new document with AI enabled  
    await testUtils.login()
    await testUtils.createDocumentFromTemplate()
    await page.waitForSelector('[data-testid="eko-document-editor"]', { timeout: 10000 })

    const editor = await testUtils.waitForEditor()
    
    // Right click
    await editor.click({ button: 'right' })
    
    // Should show AI Suggestions option
    await expect(page.locator('text=AI Suggestions')).toBeVisible()
  })

  test('should position context menu correctly near cursor', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click at a specific position
    await editor.click({ button: 'right', position: { x: 200, y: 150 } })
    
    const contextMenu = page.locator('[data-testid="right-click-context-menu"]')
    await expect(contextMenu).toBeVisible()
    
    // Menu should be positioned near the click position
    const menuBox = await contextMenu.boundingBox()
    expect(menuBox).toBeTruthy()
    
    if (menuBox) {
      // Menu should be near the click position (allowing for viewport adjustments)
      expect(menuBox.x).toBeGreaterThan(150)
      expect(menuBox.x).toBeLessThan(250)
      expect(menuBox.y).toBeGreaterThan(100)
      expect(menuBox.y).toBeLessThan(200)
    }
  })

  test('should not show context menu when right-clicking outside editor', async ({ page }) => {
    // Right click outside the editor
    await page.click('body', { button: 'right', position: { x: 50, y: 50 } })
    
    // Context menu should not be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).not.toBeVisible()
  })

  test('should handle disabled menu items correctly', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click without selecting text
    await editor.click({ button: 'right' })
    
    // Copy and Cut should be disabled when no text is selected
    const copyButton = page.locator('text=Copy')
    const cutButton = page.locator('text=Cut')
    
    if (await copyButton.isVisible()) {
      await expect(copyButton).toHaveAttribute('disabled', '')
    }
    if (await cutButton.isVisible()) {
      await expect(cutButton).toHaveAttribute('disabled', '')
    }
  })
})
