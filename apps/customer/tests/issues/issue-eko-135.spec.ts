import { test, expect } from '@playwright/test'

test.describe('EKO-135: Move charts to Shadcn/Recharts', () => {
  test.beforeEach(async ({ page }) => {
    // Mock login state to avoid authentication flow
    await page.goto('/login')
    await page.waitForLoadState('networkidle')
    
    // Mock the user being logged in by setting localStorage
    await page.evaluate(() => {
      window.localStorage.setItem('mockLoggedIn', 'true')
    })
    
    await page.goto('/customer')
    await page.waitForLoadState('networkidle')
  })

  test('should render Recharts-based charts when chart data is present', async ({ page }) => {
    // Wait for the page to load
    await page.waitForSelector('[data-testid="document-editor"], .document-editor, .editor-container', { timeout: 10000 })
    
    // Create a chart node for testing - simulate adding chart content
    const chartData = {
      type: 'bar',
      title: 'Test Chart',
      description: 'A test chart for EKO-135',
      data: [
        { month: 'Jan', revenue: 100, profit: 20 },
        { month: 'Feb', revenue: 150, profit: 35 },
        { month: 'Mar', revenue: 120, profit: 25 }
      ],
      config: {
        revenue: { label: 'Revenue', color: 'hsl(210, 70%, 50%)' },
        profit: { label: 'Profit', color: 'hsl(120, 70%, 50%)' }
      }
    }
    
    // Inject chart data into the editor
    await page.evaluate((data) => {
      const encodedData = btoa(JSON.stringify(data))
      
      // Create a chart element manually
      const chartElement = document.createElement('chart')
      chartElement.setAttribute('data-json', encodedData)
      chartElement.textContent = JSON.stringify(data)
      
      // Find the editor container and append the chart
      const editorContainer = document.querySelector('[contenteditable="true"], .ProseMirror, .tiptap') as HTMLElement
      if (editorContainer) {
        editorContainer.appendChild(chartElement)
        
        // Trigger a change event to simulate the editor processing
        const event = new Event('input', { bubbles: true })
        editorContainer.dispatchEvent(event)
      }
    }, chartData)
    
    // Wait for the chart to be rendered
    await page.waitForSelector('.chart-wrapper', { timeout: 5000 })
    
    // Check that the chart is rendered with Recharts components
    const chartContainer = page.locator('.chart-wrapper')
    await expect(chartContainer).toBeVisible()
    
    // Check for chart title
    const chartTitle = page.locator('h3:has-text("Test Chart")')
    await expect(chartTitle).toBeVisible()
    
    // Check for chart description
    const chartDescription = page.locator('p:has-text("A test chart for EKO-135")')
    await expect(chartDescription).toBeVisible()
    
    // Check that Recharts SVG is present
    const rechartsSvg = page.locator('.chart-wrapper svg.recharts-surface')
    await expect(rechartsSvg).toBeVisible()
    
    // Check that chart data is rendered (bars should be present)
    const bars = page.locator('.recharts-bar-rectangle')
    await expect(bars).toHaveCount(6) // 3 months × 2 data series = 6 bars
  })

  test('should render legacy eCharts format with eCharts renderer', async ({ page }) => {
    // Wait for the page to load
    await page.waitForSelector('[data-testid="document-editor"], .document-editor, .editor-container', { timeout: 10000 })
    
    // Create legacy eCharts format data
    const legacyChartData = {
      title: { text: 'Legacy Chart' },
      xAxis: { type: 'category', data: ['A', 'B', 'C'] },
      yAxis: { type: 'value' },
      series: [{ data: [120, 200, 150], type: 'bar' }]
    }
    
    // Inject legacy chart data into the editor
    await page.evaluate((data) => {
      const encodedData = btoa(JSON.stringify(data))
      
      // Create a chart element manually
      const chartElement = document.createElement('chart')
      chartElement.setAttribute('data-json', encodedData)
      chartElement.textContent = JSON.stringify(data)
      
      // Find the editor container and append the chart
      const editorContainer = document.querySelector('[contenteditable="true"], .ProseMirror, .tiptap') as HTMLElement
      if (editorContainer) {
        editorContainer.appendChild(chartElement)
        
        // Trigger a change event to simulate the editor processing
        const event = new Event('input', { bubbles: true })
        editorContainer.dispatchEvent(event)
      }
    }, legacyChartData)
    
    // Wait for the legacy chart to render with eCharts
    await page.waitForSelector('canvas, svg', { timeout: 5000 })
    
    // Check that the chart is rendered (eCharts creates a canvas element)
    const chartCanvas = page.locator('canvas')
    await expect(chartCanvas).toBeVisible()
    
    // Verify this is a legacy chart by checking for absence of Recharts elements
    const rechartsElements = page.locator('.recharts-wrapper')
    await expect(rechartsElements).toHaveCount(0)
  })

  test('should display error for invalid chart data', async ({ page }) => {
    // Wait for the page to load
    await page.waitForSelector('[data-testid="document-editor"], .document-editor, .editor-container', { timeout: 10000 })
    
    // Create invalid chart data
    const invalidChartData = 'invalid json{'
    
    // Inject invalid chart data into the editor
    await page.evaluate((data) => {
      const encodedData = btoa(data)
      
      // Create a chart element manually
      const chartElement = document.createElement('chart')
      chartElement.setAttribute('data-json', encodedData)
      chartElement.textContent = data
      
      // Find the editor container and append the chart
      const editorContainer = document.querySelector('[contenteditable="true"], .ProseMirror, .tiptap') as HTMLElement
      if (editorContainer) {
        editorContainer.appendChild(chartElement)
        
        // Trigger a change event to simulate the editor processing
        const event = new Event('input', { bubbles: true })
        editorContainer.dispatchEvent(event)
      }
    }, invalidChartData)
    
    // Wait for the error message to appear
    await page.waitForSelector('.chart-error', { timeout: 5000 })
    
    // Check that error message is displayed for invalid JSON
    const errorMessage = page.locator('.chart-error')
    await expect(errorMessage).toBeVisible()
    await expect(errorMessage).toContainText('Invalid chart JSON')
  })

  test('should render different chart types correctly', async ({ page }) => {
    // Wait for the page to load
    await page.waitForSelector('[data-testid="document-editor"], .document-editor, .editor-container', { timeout: 10000 })
    
    const chartTypes = [
      {
        type: 'area',
        data: [
          { month: 'Jan', value: 100 },
          { month: 'Feb', value: 150 },
          { month: 'Mar', value: 120 }
        ],
        config: { value: { label: 'Value', color: 'hsl(210, 70%, 50%)' } }
      },
      {
        type: 'line',
        data: [
          { day: 'Mon', temp: 20 },
          { day: 'Tue', temp: 22 },
          { day: 'Wed', temp: 18 }
        ],
        config: { temp: { label: 'Temperature', color: 'hsl(0, 70%, 50%)' } }
      },
      {
        type: 'pie',
        data: [
          { category: 'A', value: 30 },
          { category: 'B', value: 45 },
          { category: 'C', value: 25 }
        ],
        config: {
          A: { label: 'Category A', color: 'hsl(0, 70%, 50%)' },
          B: { label: 'Category B', color: 'hsl(120, 70%, 50%)' },
          C: { label: 'Category C', color: 'hsl(240, 70%, 50%)' }
        }
      }
    ]
    
    for (const [index, chartConfig] of chartTypes.entries()) {
      // Inject chart data into the editor
      await page.evaluate(({ data, index }: { data: any, index: number }) => {
        const encodedData = btoa(JSON.stringify(data))
        
        // Create a chart element manually
        const chartElement = document.createElement('chart')
        chartElement.setAttribute('data-json', encodedData)
        chartElement.textContent = JSON.stringify(data)
        chartElement.id = `chart-${index}`
        
        // Find the editor container and append the chart
        const editorContainer = document.querySelector('[contenteditable="true"], .ProseMirror, .tiptap') as HTMLElement
        if (editorContainer) {
          editorContainer.appendChild(chartElement)
          
          // Trigger a change event to simulate the editor processing
          const event = new Event('input', { bubbles: true })
          editorContainer.dispatchEvent(event)
        }
      }, { data: chartConfig, index })
      
      // Wait for the chart to be rendered
      await page.waitForSelector(`#chart-${index} + * .chart-wrapper`, { timeout: 5000 })
      
      // Check that the chart is rendered with appropriate SVG elements
      const rechartsSvg = page.locator('.chart-wrapper svg.recharts-surface').nth(index)
      await expect(rechartsSvg).toBeVisible()
    }
    
    // Verify all three charts are present
    const allCharts = page.locator('.chart-wrapper')
    await expect(allCharts).toHaveCount(3)
  })
})