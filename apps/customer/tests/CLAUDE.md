# Tests

This directory contains comprehensive Playwright tests for the Customer App

# Guidelines

## Bad Practices for Writing Playwright Tests

Here are common bad practices to avoid when writing Playwright tests:

## Selector Issues

**Using fragile selectors** - Relying on CSS selectors that break easily when the UI changes, like
`div > div:nth-child(3) > span`.
IMPORTANT ONLY use data attributes, accessible roles, or stable IDs, never css. If you need to change the code being
tested to introduce a `data-testid` attribute then please do so.

**Hard-coding selectors everywhere** - Repeating the same selectors across multiple tests makes maintenance difficult.
We have the apps/customer/tests/helpers/tests directory for this, make use of those utils. If you need to create new
utils files in that directory then please do so.

**Not using <PERSON><PERSON>'s locator strategy** - Using `page.$()` instead of `page.locator()` misses out on auto-waiting
and better error messages.

## Timing and Waiting

**Using arbitrary sleeps** - Adding `await page.waitForTimeout(5000)` instead of waiting for specific conditions. This
makes tests slow and flaky.

**Not waiting for elements properly** - Forgetting to wait for elements to be visible, enabled, or loaded before
interacting with them.

**Assuming synchronous behavior** - Not accounting for network requests, animations, or dynamic content loading.

## Test Structure

**Writing overly long tests** - Creating tests that do too many things at once, making them hard to debug and understand
when they fail.

**Poor test isolation** - Tests depending on the state left by previous tests or sharing data between test cases.

**Not cleaning up test data** - Leaving test artifacts in databases or systems that can affect subsequent test runs.

## Authentication and State

**Re-authenticating in every test** - Logging in manually for each test instead of using Playwright's authentication
features or storage state.

**Hard-coding credentials** - Putting usernames and passwords directly in test code instead of using environment
variables or secure storage.

## Error Handling

**Ignoring test failures** - Not investigating flaky tests or suppressing failures without understanding root causes.

**Insufficient error context** - Not adding meaningful test descriptions or custom error messages that help with
debugging.

**Not using proper assertions** - Using generic assertions instead of Playwright's specific matchers that provide better
error messages.
</guidelines>

## Test Structure

### Core Test Files

- **`report-system.spec.ts`** - Main functionality tests for report components, templates, and editor features
- **`document-templates.spec.ts`** - Tests for template management, selection, and document creation
- **`report-api.spec.ts`** - API endpoint tests for report generation and data fetching
- **`nested-component-loading.spec.ts`** - **NEW** - Comprehensive tests for nested components with charts and citations
- **`report-section-loading.spec.ts`** - Report section loading behavior and state management
- **`document-entity-run-selector.spec.ts`** - Entity and run selection functionality
- **`integration/report-workflow.spec.ts`** - End-to-end workflow tests and integration scenarios

### Helper Files

- **`helpers/tests/test-utils.ts`** - Common utilities and helper functions for tests

### Test Commands

```bash
# Run specific test file
npx playwright test report-system.spec.ts

# Run the new nested component loading tests
npx playwright test nested-component-loading.spec.ts

# Run tests in specific browser
npx playwright test --project=chromium

```

# Test Configuration

Tests are configured in `playwright.config.ts` with:
- Base URL: `http://localhost:3000`

## Test Data and Setup

### Mock Data

Tests include mocking capabilities for:
- API responses
- Error conditions
- Network failures
- Large datasets

## Writing New Tests

### Using Test Utilities

```typescript
import { TestUtils } from '../helpers/tests/test-utils';

test('my test', async ({ page }) => {
  const testUtils = new TestUtils(page);
  
  // Login
  await testUtils.login();
  
  // Create document
  const documentId = await testUtils.createDocumentFromTemplate();
  
  // Add component
  await testUtils.addReportComponent('section', {
    id: 'my-section',
    title: 'My Section'
  });
  
  // Verify
  await testUtils.checkComponentExists('my-section');
});
```

### Best Practices

1. **Use descriptive test names** that explain what is being tested
2. **Group related tests** using `test.describe()`
3. **Use test utilities** for common operations
4. **Mock external dependencies** when testing error conditions
5. **Clean up after tests** (automatic with Playwright)
6. **Test both happy path and error cases**
7. **Include accessibility and mobile tests**
8. **Don't use CSS based selectors**, add data-testid attributes to the code base as needed

### Test Patterns

```typescript
// Component interaction pattern
await testUtils.openComponentConfig('.report-section');
await testUtils.fillComponentConfig({ title: 'New Title' });
await testUtils.confirmComponentConfig();

// API testing pattern
const response = await page.request.get('/api/endpoint');
expect(response.status()).toBe(200);
const data = await response.json();
expect(data).toHaveProperty('text');

// Error handling pattern
await testUtils.mockApiError('/api/endpoint');
await expect(page.locator('.error')).toBeVisible();
```

## Debugging Tests

### Visual Debugging

```bash
# Run with UI for visual debugging
npm run test:ui

# Run in headed mode to see browser
npm run test:headed

# Debug specific test
npx playwright test --debug report-system.spec.ts
```

### Screenshots and Videos

Playwright automatically captures:
- Screenshots on failure
- Videos of test runs (in CI)
- Traces for debugging

### Common Issues

1. **Timing Issues**: Use `waitFor` methods instead of fixed timeouts
2. **Element Not Found**: Check selectors and wait for elements to be visible
3. **Authentication**: Ensure login flow works correctly
4. **API Mocking**: Verify mock responses match expected format

## Continuous Integration

Tests are designed to run in CI environments with:
- Headless browser execution
- Retry on failure
- Parallel execution disabled for stability
- Automatic browser installation

## Maintenance

### Updating Tests

When adding new features:
1. Add corresponding test cases
2. Update test utilities if needed
3. Verify all existing tests still pass
4. Update this file if test structure changes

### Performance Monitoring

Tests include performance checks for:
- Component loading times
- Large document handling
- API response times
- Memory usage patterns

Monitor test execution times and update timeouts as needed.
