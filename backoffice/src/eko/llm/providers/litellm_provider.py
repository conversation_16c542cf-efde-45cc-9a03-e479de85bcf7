import json
import os
from litellm import completion_cost, completion, token_counter, supports_reasoning
from typing import Dict, List, Type, TypeVar, Union, Any, Optional, cast

import instructor
import litellm
from loguru import logger
from pydantic import BaseModel

from eko import console
from eko.llm import LLMModel
from eko.llm.providers.base import LL<PERSON><PERSON>ider, NoneResponseError
from eko.settings import settings
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

# Forward declaration to avoid circular import
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from eko.llm.main import LLMOptions

class LiteLLMProvider(LLMProvider):
    """
    LiteLLM provider that supports multiple model providers through a unified interface.

    This provider uses litellm to handle different model providers including:
    - OpenAI
    - Anthropic
    - Google (Vertex AI)
    - and many others
    """

    def __init__(self,
                 model_prefix: str = None,
                 api_key: str = None,
                 base_url: str = None,
                 use_instructor: bool = True,
                 instructor_mode=instructor.Mode.TOOLS,
                 vertex_project_id: str = None,
                 vertex_location: str = None
                 ):
        """
        Initialize the LiteLLM provider.

        Args:
            model_prefix: Optional prefix to add to model names (e.g., "vertex_ai/")
            api_key: API key for the provider
            base_url: Base URL for the provider
            use_instructor: Whether to use instructor for structured outputs
            instructor_mode: Mode to use with instructor
            vertex_project_id: Google Cloud project ID for Vertex AI
            vertex_location: Google Cloud location for Vertex AI
        """
        super().__init__()
        self.model_prefix = model_prefix
        self.api_key = api_key
        self.base_url = base_url
        self.use_instructor = use_instructor
        self.instructor_mode = instructor_mode
        self.vertex_project_id = vertex_project_id
        self.vertex_location = vertex_location

        # Configure litellm for Vertex AI if needed
        if vertex_project_id and vertex_location:
            logger.info(f"Configuring litellm for Vertex AI with project {vertex_project_id} in {vertex_location}")
            os.environ["VERTEX_PROJECT"] = vertex_project_id
            os.environ["VERTEX_LOCATION"] = vertex_location


        logger.info(f"Initialized LiteLLM provider with model_prefix={model_prefix}")

    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the full model name with prefix if needed.

        Args:
            llm: The LLM model to use

        Returns:
            The full model name
        """
        model_name = llm.value.name

        # Add prefix if specified and not already in the model name
        if self.model_prefix and not model_name.startswith(self.model_prefix):
            return f"{self.model_prefix}{model_name}"

        return model_name

    def call_chat(
        self,
        llm: LLMModel,
        messages: List[Dict],
        max_tokens: int = 4000,
        response_model: Union[None, Type[T]] = None,
        temperature=0.0,
        metadata: dict = {},
        options: Union["LLMOptions", None] = None,
    ) -> Union[str, Optional[T]]:
        """
        Call the LLM with chat messages and return response.

        Args:
            llm: The LLM model to use
            messages: List of chat messages
            max_tokens: Maximum tokens in the response
            response_model: Optional Pydantic model for response parsing
            temperature: Temperature for sampling
            metadata: Additional metadata

        Returns:
            A string response or Pydantic model instance
        """
        try:
            if options is None:
                options = LLMOptions()
            # Clean messages by removing cache control
            messages = self.remove_cache_control(messages)
            model_name = self.get_model_name(llm)

            # Prepare additional parameters
            params = {
                "model": model_name,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
            }

            if options.thinking == False and supports_reasoning(model=model_name) and "2.5-flash-preview" in model_name.lower():
                params["thinkingConfig"] = {
                    "includeThoughts": False,
                    "thinkingBudget": 0,  # Set a zero budget
                }

            if  options.thinking == True and supports_reasoning(model=model_name) and "2.5-flash-preview" in model_name.lower():
                params['thinkingConfig'] ={
                    'includeThoughts': True,
                    'thinkingBudget': options.thinking_budget if options.thinking_budget is not None else max_tokens // 2  # Set a non-zero budget
                }

                # Add API key and base URL if provided
            if self.api_key:
                params["api_key"] = self.api_key
            if self.base_url:
                params["api_base"] = self.base_url
                
            if options and options.ground_with_search and "gemini" in model_name.lower():
                params["tools"]=[
                    {
                        "googleSearchRetrieval": {
                            "dynamicRetrievalConfig": {
                                "mode": "MODE_DYNAMIC",
                                "dynamicThreshold": 0.7
                            }
                        }
                    }
                ],

            # For Vertex AI models, add project and location
            if self.vertex_project_id and self.vertex_location and "vertex" in model_name.lower():
                params["vertex_project"] = self.vertex_project_id
                params["vertex_location"] = self.vertex_location
                params["base_model"] = llm.value.base_model

            logger.info(f"Calling {model_name} with {len(messages)} messages")


            if response_model is None:
                # Get raw text response
                response = litellm.completion(**params)
                answer = response.choices[0].message.content
                # pass your response from completion to completion_cost
                logger.debug(f"Text based answer: {answer}")
                self.display_cost(llm, model_name, response, messages)
                if answer is None:
                    logger.error(messages)
                    logger.error(response)
                    return None
                return cast(str, answer)
            else:
                # Get structured response with instructor
                if self.use_instructor:
                    client = instructor.from_litellm(completion, mode=self.instructor_mode, model=self.get_model_name(llm))
                    response, raw_completion = client.chat.completions.create_with_completion(
                        response_model=response_model,
                        **params
                    )
                else:
                    raise NotImplementedError("Structured output with instructor not supported for this provider")
                if (not options or not options.none_responses) and response is None:
                    logger.error(messages)
                    logger.error(response)
                    return None
                if settings.llm_record_cost_in_db:
                    self.display_cost(llm, model_name, response, messages, raw_completion)
                return cast(T, response)

        except Exception as e:
            logger.error(f"Error calling LiteLLM: {e}")
            # logger.exception(e)
            raise e

    def display_cost(self, llm: LLMModel, model_name, response, messages, raw_completion=None):
        cost= 0.0
        if raw_completion is None:
            if type(response) == str:
                cost = self.calculate_cost(response)
                if settings.flag_persistent_llm_usage_info:
                    self._store_cost_in_db(
                        self.__class__.__name__,
                        model_name,
                        token_counter(model=model_name, messages=messages),
                        token_counter(model=model_name, text=response),
                        cost,
                        messages,
                        response,
                    )
            elif hasattr(response, 'choices') and len(response.choices) > 0:
                cost = self.calculate_cost( response)
                if settings.flag_persistent_llm_usage_info:
                    self._store_cost_in_db(self.__class__.__name__, model_name, response.usage.prompt_tokens, response.usage.completion_tokens, cost, messages, response.choices[0].message.content)
            else:
                logger.warning(f"Response is not recognized: {response} cannot track cost!")
            if cost == 0.0:
                logger.debug(f"Cost is 0.0 for {model_name} with {response}")
                cost = ((llm.value.token_pricing_per_million[0] / 1_000_000.00) * response.usage.completion_tokens
                              + (llm.value.token_pricing_per_million[1] / 1_000_000.00) * response.usage.prompt_tokens)
        else:
            logger.debug(f"Response is not a string: {response} cannot track cost in but we're going to estimate!")
            logger.debug(str(raw_completion))
            cost = raw_completion._hidden_params["response_cost"]
            if settings.flag_persistent_llm_usage_info:
                self._store_cost_in_db(self.__class__.__name__, model_name, raw_completion.usage.prompt_tokens, raw_completion.usage.completion_tokens, cost, messages, raw_completion.choices[0].message.content)
            if cost == 0.0:
                cost = ((llm.value.token_pricing_per_million[0] / 1_000_000.00) * response.usage.completion_tokens
                              + (llm.value.token_pricing_per_million[1] / 1_000_000.00) * response.usage.prompt_tokens)
        LLMProvider.running_total = LLMProvider.running_total + cost
        logger.info(f"MODEL COST {model_name} = {(cost * 100):,.4f}c - TOTAL = {(LLMProvider.running_total * 100):,.2f}c")

    def calculate_cost(self, response):
        try:
            return completion_cost(completion_response=response)
        except Exception as e:
            logger.warning(f"Unable to calculate cost for response: {e}")
        return 0.0

    def call_chat_with_tools(self, llm: LLMModel, messages: List[Dict], tools: List[Dict],
                           max_tokens: int = 4000, temperature=0.0, metadata: dict = {},
                           options: Union['LLMOptions', None] = None) -> Any:
        """
        Call the LLM with tools/function calling.

        Args:
            llm: The LLM model to use
            messages: List of chat messages
            tools: List of tool definitions with implementations
            max_tokens: Maximum tokens in the response
            temperature: Temperature for sampling
            metadata: Additional metadata

        Returns:
            The final response after tool interactions
        """
        # Extract API-compatible tool definitions and their implementations
        api_tools, implementations = self._prepare_tools_for_api(tools)
        model_name = self.get_model_name(llm)

        # For Anthropic models, ensure modify_params is set to True
        # This adds a dummy tool to the request when needed
        if "anthropic" in model_name.lower() and not getattr(litellm, "modify_params", False):
            logger.info("Setting litellm.modify_params=True for Anthropic model")
            litellm.modify_params = True

        # Start the conversation loop
        iteration = 0
        current_messages = messages.copy()
        max_tool_iterations = 25

        while iteration < max_tool_iterations:
            iteration += 1
            logger.debug(f"Tool iteration {iteration}/{max_tool_iterations}")

            # Call the model
            try:
                params = {
                    "model": model_name,
                    "messages": current_messages,
                    "max_tokens": max_tokens,
                    "tools": api_tools,
                    "temperature": temperature,
                    "tool_choice": "auto",
                }

                # Add API key and base URL if provided
                if self.api_key:
                    params["api_key"] = self.api_key
                if self.base_url:
                    params["api_base"] = self.base_url

                # For Vertex AI models, add project and location
                if self.vertex_project_id and self.vertex_location and "vertex" in model_name.lower():
                    params["vertex_project"] = self.vertex_project_id
                    params["vertex_location"] = self.vertex_location

                response = litellm.completion(**params)

                # Check if the model wants to use a tool
                message = response.choices[0].message
                current_messages.append({"role": "assistant", "content": message.content, "tool_calls": message.tool_calls})

                # If no tool calls, we're done
                if not message.tool_calls:
                    break

                # Process each tool call
                for tool_call in message.tool_calls:
                    try:
                        # Parse the function arguments
                        function_args = json.loads(tool_call.function.arguments)

                        # Execute the tool
                        tool_result = self.execute_tool_call(
                            {"function": {"name": tool_call.function.name, "arguments": function_args}},
                            implementations
                        )

                        # Add the tool result to the messages
                        current_messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "name": tool_call.function.name,
                            "content": json.dumps(tool_result) if not isinstance(tool_result, str) else tool_result
                        })

                        logger.debug(f"Executed tool {tool_call.function.name}")
                    except Exception as e:
                        # Add error as tool result
                        current_messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "name": tool_call.function.name,
                            "content": f"Error: {str(e)}"
                        })
                        logger.exception(f"Error executing tool {tool_call.function.name}: {e}")

            except Exception as e:
                logger.error(f"Error in tool calling loop: {e}")
                logger.exception(e)
                console.print_exception(show_locals=False)
                break

        # Get final response
        try:
            final_params = {
                "model": model_name,
                "messages": current_messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
            }

            # Add API key and base URL if provided
            if self.api_key:
                final_params["api_key"] = self.api_key
            if self.base_url:
                final_params["api_base"] = self.base_url

            # For Vertex AI models, add project and location
            if self.vertex_project_id and self.vertex_location and "vertex" in model_name.lower():
                final_params["vertex_project"] = self.vertex_project_id
                final_params["vertex_location"] = self.vertex_location

            final_response = litellm.completion(**final_params)
            return final_response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error getting final response: {e}")
            logger.exception(e)
            # Return the last assistant message if available
            for msg in reversed(current_messages):
                if msg.get("role") == "assistant" and msg.get("content"):
                    return msg["content"]
            console.print_exception(show_locals=False)
            raise e

    def _prepare_tools_for_api(self, tools: List[Dict]) -> tuple:
        """
        Extract API-compatible tool definitions and their implementations

        Args:
            tools: List of tool definitions with implementations

        Returns:
            Tuple of (api_tools, implementations)
        """
        api_tools = []
        implementations = {}

        for tool in tools:
            # Extract the implementation
            implementation = tool.pop("implementation", None)
            if implementation:
                tool_name = tool.get("function", {}).get("name")
                if tool_name:
                    implementations[tool_name] = implementation

            # Add the API-compatible tool definition
            api_tools.append(tool)

        return api_tools, implementations
